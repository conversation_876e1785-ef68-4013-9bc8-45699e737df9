package cn.huolala.arch.hermes.mesh.sentinel.service.rpc;

import cn.huolala.arch.hermes.mesh.api.RpcRegistryService;
import cn.huolala.arch.hermes.mesh.api.bos.registry.HealthInstanceBo;
import cn.huolala.arch.hermes.mesh.common.GlobalConstants;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.dao.entity.AppInstance;
import cn.huolala.arch.hermes.mesh.dao.entity.AppMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppRegistry;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppMetadataMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppRegistryMapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import jakarta.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Optional.ofNullable;

public class AbstractActuatorService {

    public static final int MESH_PORT = 10081;

    @Resource
    protected CmdbFacadeService cmdbFacadeService;

    @Resource
    protected AppMetadataMapper appMetadataMapper;

    @Resource
    protected AppRegistryMapper appRegistryMapper;

    @Resource
    protected RpcRegistryService rpcRegistryService;

    /**
     * 调用上线，确定调用端口
     */
    protected Optional<Integer> confirmMonitorPort(String appId, String host, String instanceId, List<HealthInstanceBo> onlineAppVo) {
        //1.PHP直接返回GoProxy的端口
        if (phpAppId(appId)) {
            return Optional.of(GlobalConstants.GO_PROXY_PORT);
        }

        AppInstance appInstance = null;
        /*
         * 2.DB找实例的下线信息
         * 存在：证明是通过SOA-ADMIN操作下线的
         * 不存在：不通过SOA-ADMIN操作下线
         */
        List<AppRegistry> appRegistry = appRegistryMapper.selectByInstanceId(instanceId);
        if (CollUtil.isNotEmpty(appRegistry)) {
            appInstance = appRegistry.stream().findFirst().get().getAppVo();
        } else {
            //获取DB里面已经标记为删除最新数据作为基本依据再次上线
            AppRegistry oldDelAppRegistry = appRegistryMapper.selectLastDeletedByInstanceId(instanceId);
            if (Objects.nonNull(oldDelAppRegistry)) {
                appInstance = oldDelAppRegistry.getAppVo();
            }
        }

        //3.DB找到下线记录则直接提取Port
        if (Objects.nonNull(appInstance)) {

            if (meshAppIdByDB(appInstance)) {
                return Optional.of(MESH_PORT);
            }

            return ofNullable(appInstance.getOnOffLinePort()).isPresent() ? Optional.of(appInstance.getOnOffLinePort()) :
                    ofNullable(appInstance.getPort());

        }

        //4.从在线实例确定Port
        if (CollUtil.isNotEmpty(onlineAppVo)) {
            //获取在线实例，确定在线实例的上下线端口
            HealthInstanceBo healthInstanceBo = onlineAppVo.stream().findFirst().get();
            if (meshAppIdByConsul(healthInstanceBo)) {
                return Optional.of(MESH_PORT);
            }
            return confirmMonitorPort(healthInstanceBo, host);
        }
        return Optional.empty();
    }

    protected Optional<Integer> confirmMonitorPort(HealthInstanceBo healthInstanceBo, String targetHost) {
        String appId = healthInstanceBo.getName();

        //1.PHP直接返回GoProxy的端口
        if (phpAppId(appId)) {
            return Optional.of(GlobalConstants.GO_PROXY_PORT);
        }

        if (meshAppIdByConsul(healthInstanceBo)) {
            return Optional.of(MESH_PORT);
        }

        List<String> tags = healthInstanceBo.getTags();

        //2.包含tag management就说明是同一个端口，直接返回
        if (tags.contains("management")) {
            return ofNullable(healthInstanceBo.getPort());
        }
        List<HealthInstanceBo> onlineManagement = rpcRegistryService.healthInstances(appId + "-management", s -> true)
                .getData();

        //3.找同一个节点的monitor端口
        Optional<HealthInstanceBo> monitorAppBo = onlineManagement.stream()
                .filter(v -> targetHost.equals(v.getHost()))
                .findFirst();

        //4.不存在同一节点找第一个作为兜底
        if (monitorAppBo.isPresent()) {
            return ofNullable(monitorAppBo.get().getPort());
        }
        //5.默认找到第一个monitor注册实例的端口
        if (CollUtil.isNotEmpty(onlineManagement)) {
            return ofNullable(onlineManagement.stream().findFirst().get().getPort());
        }

        //6.查找启动参数配置是否存在monitor端口
        AppMetadata appMetadata = appMetadataMapper.selectLastByAppId(appId);
        if (appMetadata != null) {
            return ofNullable(Convert.toInt(ofNullable(appMetadata.getSystemConfig()).orElse(new HashMap<>())
                    .getOrDefault("management.server.port", healthInstanceBo.getPort())));
        }

        //7. 如果不存在monitor节点 则使用默认注册端口
        return ofNullable(healthInstanceBo.getPort());
    }

    protected boolean phpAppId(String appId) {
        return cmdbFacadeService.baseInfo(appId).getTypeStr().equalsIgnoreCase("php");
    }

    protected boolean javaAppId(String appId) {
        return cmdbFacadeService.baseInfo(appId).getTypeStr().equalsIgnoreCase("java");
    }

    protected boolean meshAppIdByConsul(HealthInstanceBo healthInstanceBo) {
        return healthInstanceBo.getTags().stream().anyMatch(tag -> tag.startsWith("Mesh="));
    }

    protected boolean meshAppIdByDB(AppInstance appInstance) {
        return appInstance.getCustomTags().containsKey("Mesh");
    }

}
