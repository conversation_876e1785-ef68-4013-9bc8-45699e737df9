package cn.huolala.arch.hermes.mesh.sentinel.service;

import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiAuthConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiCircuitConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiDegradeConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiDynamicLogConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiLimitConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiTimeoutConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ServiceAuthCommandKeyBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.degrade.ProviderDegradeBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.limit.ApiLimitCommandKeyBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.limit.ProviderLimitBo;
import cn.huolala.arch.hermes.mesh.common.enums.ApiType;
import cn.huolala.arch.hermes.mesh.common.funcation.Callback;
import cn.huolala.arch.hermes.mesh.component.apollo.dto.ItemDTO;
import cn.huolala.arch.hermes.mesh.dao.entity.ApiAuthAppConfig;

import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;

public interface GovernanceService {

    /**
     * 服务治理锁
     */
    Lock getLock(String appId, String cluster);

    /**
     * 获取限流配置
     */
    ApiLimitConfigBo apiLimitConfig(ApiType apiType, String name, List<ItemDTO> items);

    /**
     * provider 限流配置
     */
    ProviderLimitBo.MethodLimitBo providerLimitKeyConfig(String name, List<ItemDTO> itemDs);

    /**
     * 限流配置更新
     */
    void apiLimitConfig(String appId, ApiType apiType, String cluster, String userId, ApiLimitConfigBo apiLimitConfig, List<ItemDTO> items, Callback callback);

    /**
     * 获取熔断配置
     */
    ApiCircuitConfigBo apiCircuitConfig(ApiType apiType, String name, List<ItemDTO> items);

    /**
     * 更新熔断配置
     */
    void apiCircuitConfig(String appId, ApiType apiType, String cluster, String userId, ApiCircuitConfigBo apiCircuitConfig, List<ItemDTO> items, Callback callback);

    /**
     * 获取降级配置
     */
    ApiDegradeConfigBo apiDegradeConfig(ApiType apiType, String name, List<ItemDTO> items);

    /**
     * provider 降级配置
     */
    ProviderDegradeBo.MethodDegradeBo providerDegradeKeyConfig(String name, List<ItemDTO> items);

    /**
     * 更新降级配置
     */
    void apiDegradeConfig(String appId, ApiType apiType, String cluster, String userId, ApiDegradeConfigBo apiDegradeConfig, List<ItemDTO> items, Callback callback);

    /**
     * 获取超时配置
     */
    ApiTimeoutConfigBo apiTimeoutConfig(ApiType apiType, String name, List<ItemDTO> items);

    /**
     * 更新超时配置
     */
    void apiTimeoutConfig(String appId, ApiType apiType, String cluster, String userId, ApiTimeoutConfigBo apiTimeoutConfig, List<ItemDTO> items, Callback callback);

    /**
     * 获取动态日志配置
     */
    ApiDynamicLogConfigBo apiDynamicLogConfig(ApiType apiType, String name, List<ItemDTO> items);

    /**
     * 更新动态日志配置
     */
    void apiDynamicLogConfig(String appId, ApiType apiType, String cluster, String userId, ApiDynamicLogConfigBo apiDynamicLogConfig, List<ItemDTO> items, Callback callback);

    /**
     * 鉴权配置
     */
    ServiceAuthCommandKeyBo providerAuthKeyConfig(String name, List<ItemDTO> items, Map<String, List<ApiAuthAppConfig>> nameToAuthAppIds);

    /**
     * 获取授权信息
     */
    Map<String, List<ApiAuthAppConfig>> apiAuthAppConfig(String appId,String cluster);

    /**
     * 更新鉴权配置
     */
    void apiAuthConfig(String appId, ApiType apiType, String cluster, String userId, ApiAuthConfigBo apiAuthConfigBo, List<ItemDTO> items, Callback callback);


}
