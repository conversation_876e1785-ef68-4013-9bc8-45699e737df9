package cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.inspection;

import cn.huolala.arch.hermes.mesh.common.enums.AlertEventType;
import cn.huolala.arch.hermes.mesh.component.lark.model.message.ContentConstant;
import cn.huolala.arch.hermes.mesh.component.lark.notify.annotation.*;
import cn.huolala.arch.hermes.mesh.sentinel.altert.annotation.LarkNotifyData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.converter.EnvIdConverter;
import cn.huolala.arch.hermes.mesh.sentinel.altert.converter.TimeConverter;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.AppAlertDataObject;
import cn.huolala.arch.hermes.mesh.sentinel.support.InspectionConstants;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
@SuperBuilder
@LarkNotifyData(type = AlertEventType.INSPECTION_WARNING, clickUrl = @LarkMsgBottomUrl(url = InspectionConstants.INSPECTION_URL_TEMPLATE))
public class InspectionWarningData extends ApplicationEvent {

    @LarkNotifyUser
    private String owner;

    @LarkNotifyGroup
    private String notifyGroup;

    @LarkMsgContent(value = "告警详情")
    private String content;

    @LarkMsgUrlFormat(value = "reportId")
    private Long reportId;

    @LarkMsgUrlFormat(value = "panelDomain")
    private String panelDomain;

    @LarkMsgUrlFormat(value = "componentName")
    @LarkMsgContent(value = ContentConstant.Base.COMPONENT_KEY, order = 1)
    private String componentName;

    @LarkMsgUrlFormat(value = "envId")
    @LarkMsgContent(value = ContentConstant.Base.ENV_KEY, converter = EnvIdConverter.class, order = 2)
    private long areaEnvId;

    @LarkMsgContent(value = ContentConstant.Base.TRIGGER_TIME_KEY, order = 4, converter = TimeConverter.class)
    @Builder.Default
    private Long triggerTime = System.currentTimeMillis();
}
