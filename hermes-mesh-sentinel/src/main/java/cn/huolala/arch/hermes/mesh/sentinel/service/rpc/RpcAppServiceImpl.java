package cn.huolala.arch.hermes.mesh.sentinel.service.rpc;

import cn.huolala.arch.hermes.mesh.api.RpcAppService;
import cn.huolala.arch.hermes.mesh.api.bos.PointBo;
import cn.huolala.arch.hermes.mesh.api.bos.SeriesBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppEventBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppGrayConditionBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppGroupInfoBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppSoaInfo;
import cn.huolala.arch.hermes.mesh.api.bos.app.InstanceBo;
import cn.huolala.arch.hermes.mesh.api.bos.event.ApiCircuitEventLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.registry.HealthInstanceBo;
import cn.huolala.arch.hermes.mesh.api.governance.GovernanceConstants;
import cn.huolala.arch.hermes.mesh.api.governance.GovernanceTransform;
import cn.huolala.arch.hermes.mesh.common.GlobalConstants;
import cn.huolala.arch.hermes.mesh.common.enums.EventType;
import cn.huolala.arch.hermes.mesh.common.enums.InstanceStatus;
import cn.huolala.arch.hermes.mesh.common.enums.LifeEventType;
import cn.huolala.arch.hermes.mesh.common.enums.MetadataType;
import cn.huolala.arch.hermes.mesh.common.enums.RegistryEventType;
import cn.huolala.arch.hermes.mesh.common.message.ErrorMsg;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.message.RetRespMsg;
import cn.huolala.arch.hermes.mesh.common.utils.CommandUtils;
import cn.huolala.arch.hermes.mesh.common.utils.DateUtils;
import cn.huolala.arch.hermes.mesh.common.utils.InstanceUtils;
import cn.huolala.arch.hermes.mesh.component.apollo.bo.ItemBO;
import cn.huolala.arch.hermes.mesh.component.apollo.bo.NamespaceBO;
import cn.huolala.arch.hermes.mesh.component.apollo.service.ApolloFacadeConfigService;
import cn.huolala.arch.hermes.mesh.component.cmdb.exception.CmdbException;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.AppBaseInfo;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.EcsInfo;
import cn.huolala.arch.hermes.mesh.component.lone.exception.LoneException;
import cn.huolala.arch.hermes.mesh.component.lone.model.PodInfo;
import cn.huolala.arch.hermes.mesh.component.lone.service.LoneFacadeService;
import cn.huolala.arch.hermes.mesh.dao.entity.ApiCircuitEventLog;
import cn.huolala.arch.hermes.mesh.dao.entity.AppBaseEntity;
import cn.huolala.arch.hermes.mesh.dao.entity.AppDestroyLog;
import cn.huolala.arch.hermes.mesh.dao.entity.AppEvent;
import cn.huolala.arch.hermes.mesh.dao.entity.AppInstance;
import cn.huolala.arch.hermes.mesh.dao.entity.AppMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppRegistry;
import cn.huolala.arch.hermes.mesh.dao.entity.AppRuntimeMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppVersion;
import cn.huolala.arch.hermes.mesh.dao.entity.BaseEntity;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppDestroyLogMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppRuntimeMetadataMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppVersionMapper;
import cn.huolala.arch.hermes.mesh.monitor.model.GoProxyUpDown;
import cn.huolala.arch.hermes.mesh.rpc.RpcInvokerContext;
import cn.huolala.arch.hermes.mesh.sentinel.config.EndpointAuthBean;
import cn.huolala.arch.hermes.mesh.sentinel.service.AppEventService;
import cn.huolala.arch.hermes.mesh.sentinel.service.MetadataService;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.lalaframework.soa.annotation.spring.SOAServiceAutoImpl;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.mesh.api.governance.GovernanceConstants.DISCOVERY_APP_STRATEGY;
import static cn.huolala.arch.hermes.mesh.api.governance.GovernanceConstants.DISCOVERY_GLOBAL_STRATEGY;
import static cn.huolala.arch.hermes.mesh.api.governance.GovernanceConstants.DISCOVERY_STRATEGY_NODE;
import static cn.huolala.arch.hermes.mesh.common.GlobalConstants.LALA_BOOT_STARTER_SOA_KEY;
import static cn.huolala.arch.hermes.mesh.common.GlobalConstants.PHP_SOA_KEY;
import static cn.huolala.arch.hermes.mesh.common.GlobalConstants.VERSIONS;
import static cn.huolala.arch.hermes.mesh.common.utils.CommandUtils.buildInstanceId;
import static cn.huolala.arch.hermes.mesh.common.utils.CommandUtils.toTagsList;
import static cn.huolala.arch.hermes.mesh.common.utils.CommandUtils.toTagsMap;
import static cn.huolala.arch.hermes.mesh.sentinel.utils.PaginationUtil.graftPage;
import static java.util.Optional.ofNullable;


@Slf4j
@Service
@SOAServiceAutoImpl
public class RpcAppServiceImpl extends AbstractActuatorService implements RpcAppService {

    public static final String MESH_TOKEN = "oRL6z2Rhw76wYd8rDXLtDaKnc7sGvt5h";

    @Resource
    private LoneFacadeService loneFacadeService;

    @Resource
    private RpcGoProxyService rpcGoProxyService;

    @Resource
    private AppEventService appEventService;

    @Resource
    private RpcActuatorService rpcActuatorService;

    @Resource
    private ApolloFacadeConfigService configService;

    @Resource
    private AppVersionMapper appVersionMapper;

    @Resource
    private AppDestroyLogMapper appDestroyLogMapper;

    @Resource
    private AppRuntimeMetadataMapper appRuntimeMetadataMapper;

    @Resource
    private MetadataService metadataService;

    @Resource
    private EndpointAuthBean endpointAuthBean;

    @Override
    public RespMsg<List<InstanceBo>> appInstances(String appId) {

        List<InstanceBo> instanceBos = new ArrayList<>(rpcRegistryService.healthInstances(appId, this::soaTagPredicate).getData().stream().map(healthInstanceBo -> BeanUtil.copyProperties(healthInstanceBo, InstanceBo.class)).toList());
        List<AppRegistry> appRegistries = appRegistryMapper.selectByAppId(appId);

        /*
         * 离线实例ids
         */
        List<String> offInstanceIds = appRegistries.stream().map(AppBaseEntity::getInstanceId).toList();
        /*
         * 在线实例ids
         */
        List<String> onInstanceIds = instanceBos.stream().map(HealthInstanceBo::getId).toList();

        /*
         * 处理历史旧数据
         */
        Collection<String> dirtyInstanceIds = CollUtil.intersection(onInstanceIds, offInstanceIds);
        if (CollUtil.isNotEmpty(dirtyInstanceIds)) {
            /*
             * 如果下线时间在5s内 将暂时不删除下线信息作为缓冲时间
             */
            List<Long> ids = appRegistries.stream().filter(appRegistry -> DateUtil.offsetSecond(appRegistry.getCreatedAt(), 5).getTime() < DateUtil.current() && dirtyInstanceIds.contains(appRegistry.getInstanceId())).map(BaseEntity::getId).toList();
            appRegistryMapper.deleteBatchIds(ids);
            appRegistries = appRegistries.stream().filter(appRegistry -> !dirtyInstanceIds.contains(appRegistry.getInstanceId())).toList();
        }
        /*
         * 处理重复脏数据
         */
        Set<String> uniqueInstanceId = new HashSet<>();
        List<Long> repeated = appRegistries.stream().filter(appRegistry -> !uniqueInstanceId.add(appRegistry.getAppVo().getInstanceId()))
                .map(BaseEntity::getId).collect(Collectors.toList());
        if (!repeated.isEmpty()) {
            appRegistryMapper.deleteBatchIds(repeated);
            appRegistries = appRegistries.stream().filter(appRegistry -> !repeated.contains(appRegistry.getId())).toList();
        }

        /*
         * 确认当前离线实例是否存在cmdb的节点进程，并检查离线实例是否存活
         * 不能调用health端点，会导致soa认为consul健康检查，影响soa重连
         */
        List<AppInstance> offlineInstance = appRegistries.stream().map(AppRegistry::getAppVo).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(offlineInstance)) {
            Iterator<AppInstance> iterator = offlineInstance.iterator();
            List<String> hybridInstanceId = appHybridInstanceId(appId);
            while (iterator.hasNext()) {
                AppInstance appInstance = iterator.next();
                /*
                 * 正常情况下，hybridInstanceId 不为null,如果hybridInstanceId不包含记录的下线接点，则删除下线记录
                 * 如果lalaplat和cmdb任意一个抛了异常，就会返回null,就不能用来做下线节点的确认
                 */
                if (Objects.nonNull(hybridInstanceId) && (!hybridInstanceId.contains(appInstance.getInstanceId())
                        //兼容PHP注册的instanceID为ci-php-demo-api-*************
                        || !hybridInstanceId.contains(StrUtil.replace(appInstance.getInstanceId(), ".", "-")))) {
                    appRegistryMapper.deleteByInstanceId(appInstance.getInstanceId());
                    iterator.remove();
                    continue;
                }
                try {
                    RpcInvokerContext.getContext().setHost(appInstance.getHost());
                    RpcInvokerContext.getContext().setPort(ofNullable(appInstance.getOnOffLinePort()).orElse(appInstance.getPort()));
                    if (phpAppId(appId)) {
                        RpcInvokerContext.getContext().setPort(GlobalConstants.GO_PROXY_PORT);
//                        rpcGoProxyService.health(appId, "SOA-ADMIN");
                    } else {
                        rpcActuatorService.actuatorInfo();
                    }
                } catch (Exception e) {
                    //如果发生异常,则认为是服务已经被关闭
                    appRegistryMapper.deleteByInstanceId(appInstance.getInstanceId());
                    iterator.remove();
                } finally {
                    RpcInvokerContext.removeContext();
                }
            }
        }
        List<InstanceBo> offInstanceBos = appRegistries.stream().map(appRegistry -> {
            AppInstance appVo = appRegistry.getAppVo();

            InstanceBo instanceBo = new InstanceBo();
            instanceBo.setId(appVo.getInstanceId());
            instanceBo.setName(appVo.getAppId());
            instanceBo.setHost(appVo.getHost());
            instanceBo.setPort(appVo.getPort());
            instanceBo.setMetadata(appVo.getMetadata());

            instanceBo.setOffPort(appVo.getOnOffLinePort());
            instanceBo.setStatus(InstanceStatus.OFFLINE);
            instanceBo.setTags(toTagsList(appVo.getCustomTags()));
            return instanceBo;
        }).toList();
        instanceBos.addAll(offInstanceBos);
        return RespMsg.respSuc(instanceBos);
    }

    @Override
    public RespMsg<List<SeriesBo>> eventMetrics(String appId, String instanceId, Long startTime, Long endTime) {

        List<AppEvent> appEvents = appEventService.selectList(appId, instanceId, startTime, endTime);

        Map<String, List<AppEvent>> timeAppEvent = appEvents.stream().collect(Collectors.groupingBy(appEvent -> DateUtils.format(appEvent.getCreatedAt(), DateUtils.PATTERN_MM_DD_HH_MM), Collectors.toList()));

        List<SeriesBo> seriesBos = appEvents.stream().collect(Collectors.groupingBy(AppEvent::getType)).entrySet().stream().map(entry -> {
            SeriesBo seriesBo = new SeriesBo();
            seriesBo.setName(entry.getKey().name());
            seriesBo.setSeries(entry.getValue().stream().collect(Collectors.groupingBy(appEvent -> DateUtils.format(appEvent.getCreatedAt(), DateUtils.PATTERN_MM_DD_HH_MM), Collectors.toList())).entrySet().stream()
                    .map(entry1 -> PointBo.builder().name(entry1.getKey()).value(entry1.getValue().size()).extra(Map.of("type", entry.getKey().name(), "ids", entry1.getValue().stream().map(BaseEntity::getId).toList())).build()).collect(Collectors.toList()));
            return seriesBo;
        }).collect(Collectors.toList());

        seriesBos.forEach(seriesBo -> {
            List<PointBo> pointBos = seriesBo.getSeries();
            List<String> times = pointBos.stream().map(PointBo::getName).toList();
            pointBos.addAll(CollUtil.disjunction(timeAppEvent.keySet(), times).stream().map(time -> PointBo.builder().name(time).extra(Map.of("type", seriesBo.getName(), "ids", timeAppEvent.get(time).stream().map(BaseEntity::getId).toList())).build()).toList());
            pointBos.sort((o1, o2) -> (int) (timeAppEvent.get(o1.getName()).get(0).getId() - timeAppEvent.get(o2.getName()).get(0).getId()));
            seriesBo.setSeries(pointBos);
        });
        return RespMsg.respSuc(seriesBos);
    }

    @Override
    public RespMsg<Page<AppEventBo>> appEvents(String appId, List<String> ids, String instanceId, int num, int size) {
        Page<AppEvent> appEventPage;
        if (CollUtil.isNotEmpty(ids)) {
            appEventPage = appEventService.selectPage(ids, num, size);
        } else {
            appEventPage = appEventService.selectPage(appId, instanceId, num, size);
        }
        return RespMsg.respSuc(graftPage(appEventPage, appEvent -> BeanUtil.copyProperties(appEvent, AppEventBo.class)));
    }

    @Override
    public RespMsg<ApiCircuitEventLogBo> appCircuitEvent(String appId, String ip, String commandKey) {
        String instanceId = "";
        if (!StrUtil.isBlankOrUndefined(ip)) {
            instanceId = InstanceUtils.assembleInstanceId(appId, ip);
        }
        ApiCircuitEventLog apiCircuitEvent = appEventService.selectCircuitLast(appId, instanceId, commandKey);
        return RespMsg.respSuc(BeanUtil.copyProperties(apiCircuitEvent, ApiCircuitEventLogBo.class));
    }

    @Transactional
    @Override
    public RespMsg<Void> appUp(String appId, String host, String userId) {
        List<HealthInstanceBo> online = rpcRegistryService.healthInstances(appId, this::soaTagPredicate).getData();
        String instanceId = buildInstanceId(appId, host);

        //1.校验是否已在线
        Optional<HealthInstanceBo> appVoOptional = findMatchFirst(online, instanceId);
        if (appVoOptional.isPresent()) {
            appRegistryMapper.deleteByInstanceId(instanceId);
            return RespMsg.respErr(ErrorMsg.ALREADY_ONLINE);
        }

        //2.确认调用上线的端口
        Optional<Integer> port = confirmMonitorPort(appId, host, instanceId, online);
        if (port.isEmpty()) {
            return RespMsg.respErr(StrUtil.format("未获取到当前实例的AppId: {},host: {} 的上线端口，无法调用服务上线", appId, host));
        }

        AppEvent appEvent = new AppEvent();
        appEvent.setAppId(appId);
        appEvent.setInstanceId(instanceId);
        appEvent.setType(EventType.REGISTRY);
        String result = "success";
        try {
            //3.进行调用上线IP:PORT
            RpcInvokerContext.getContext().setHost(host);
            RpcInvokerContext.getContext().setPort(port.get());
            endpointAuthBean.setEndpointAuthSecret(appId,"default");
            // TODO 后续mesh按照标准Endpoint进行鉴权，删除下面一行即可
            RpcContext.getClientAttachment().putRawAttachment("token", MESH_TOKEN);
            if (phpAppId(appId)) {
                RetRespMsg<Void> response = rpcGoProxyService.registry(new GoProxyUpDown(appId, GlobalConstants.GO_PROXY_VERSION_FIELD_VALUE));
                if (!response.success()) {
                    result = StrUtil.format("调用PHP服务上线异常,host: {},port: {},{}", host, port.get(), response.getMsg());
                    return RespMsg.respErr(result);
                }
            } else {
                rpcActuatorService.registry(new HashMap<>());
            }
            appRegistryMapper.deleteByInstanceId(instanceId);
            return RespMsg.respSuc();
        } catch (Throwable t) {
            result = StrUtil.format("服务上线异常,host: {},port: {},{}", host, port.get(), t.getMessage());
            return RespMsg.respErr(result);
        } finally {
            RpcInvokerContext.removeContext();
            appEvent.setData(JSONObject.of("eventType", RegistryEventType.OPERATION_UP, "memo", "operator by :" + userId, "result", result).toString());
            appEventService.report(appEvent);
        }

    }


    @Transactional
    @Override
    public RespMsg<Void> appDown(String appId, String host, String userId, boolean record) {
        List<HealthInstanceBo> online = rpcRegistryService.healthInstances(appId, this::soaTagPredicate).getData();
        String instanceId = buildInstanceId(appId, host);

        //1.查找是否存在在线服务注册实例
        Optional<HealthInstanceBo> optionalHealthInstanceBo = findMatchFirst(online, instanceId);
        if (optionalHealthInstanceBo.isEmpty()) {
            return RespMsg.respErr(ErrorMsg.ALREADY_OFFLINE);
        }

        //2.java需要检测版本
        HealthInstanceBo healthInstanceBo = optionalHealthInstanceBo.get();
        if (javaAppId(appId)) {
            RespMsg<Void> versionCheck = versionCheck(healthInstanceBo);
            if (versionCheck != null) {
                return versionCheck;
            }
        }

        //3.确定在线实例的下线调用端口
        Optional<Integer> onOffLinePort = confirmMonitorPort(healthInstanceBo, healthInstanceBo.getHost());
        if (onOffLinePort.isEmpty()) {
            return RespMsg.respErr("无法找到下线请求端口,请联系管理员");
        }

        //4.记录上下线端口
        AppInstance appInstance = new AppInstance();
        appInstance.setAppId(appId);
        appInstance.setHost(healthInstanceBo.getHost());
        appInstance.setPort(healthInstanceBo.getPort());
        appInstance.setInstanceId(healthInstanceBo.getId());
        appInstance.setOnOffLinePort(onOffLinePort.get());
        appInstance.setCustomTags(toTagsMap(healthInstanceBo.getTags()));

        AppEvent appEvent = new AppEvent();
        appEvent.setAppId(appId);
        appEvent.setInstanceId(instanceId);
        appEvent.setType(EventType.REGISTRY);
        String result = "success";
        try {
            //4.进行调用下线IP:PORT
            RpcInvokerContext.getContext().setHost(appInstance.getHost());
            RpcInvokerContext.getContext().setPort(ofNullable(appInstance.getOnOffLinePort()).orElse(appInstance.getPort()));
            endpointAuthBean.setEndpointAuthSecret(appId,"default");
            // TODO 后续mesh按照标准Endpoint进行鉴权，删除下面一行即可
            RpcContext.getClientAttachment().putRawAttachment("token", MESH_TOKEN);
            if (phpAppId(appId)) {
                RetRespMsg<Void> registryResult = rpcGoProxyService.unRegistry(new GoProxyUpDown(appId, GlobalConstants.GO_PROXY_VERSION_FIELD_VALUE));
                if (!registryResult.success()) {
                    result = StrUtil.format("调用PHP服务下线异常,host: {},port: {},{}", healthInstanceBo.getHost(), appInstance.getOnOffLinePort(), registryResult.getMsg());
                    return RespMsg.respErr(result);
                }
            } else {
                rpcActuatorService.unRegistry(new HashMap<>());
            }
            //只有下线成功才会写入下线日志
            AppRegistry appRegistry = new AppRegistry();
            appRegistry.setAppId(appId);
            appRegistry.setInstanceId(instanceId);
            appRegistry.setAppVo(appInstance);
            if (record) {
                appRegistryMapper.insert(appRegistry);
            }
            return RespMsg.respSuc();
        } catch (Exception e) {
            result = StrUtil.format("服务下线异常,host: {},port: {},{}", healthInstanceBo.getHost(), appInstance.getOnOffLinePort(), e.getMessage());
            return RespMsg.respErr(result);
        } finally {
            RpcInvokerContext.removeContext();
            appEvent.setData(JSONObject.of("eventType", RegistryEventType.OPERATION_DOWN, "memo", "operator by :" + userId, "result", result).toString());
            appEventService.report(appEvent);
        }
    }

    @Override
    public RespMsg<Void> appDestroy(String appId, String host, String memo) {
        String instanceId = buildInstanceId(appId, host);
        //服务销毁不需要记录下线节点
        RespMsg<Void> respMsg = appDown(appId, host, memo, false);
        memo = memo + " : " + respMsg.getMsg();

        AppDestroyLog destroyLog = new AppDestroyLog();
        destroyLog.setAppId(appId);
        destroyLog.setHost(host);
        destroyLog.setStatus(respMsg.isSuccess());
        destroyLog.setMemo(memo);
        appDestroyLogMapper.insert(destroyLog);

        AppEvent appEvent = new AppEvent();
        appEvent.setAppId(appId);
        appEvent.setInstanceId(instanceId);
        appEvent.setType(EventType.LIFE);
        appEvent.setData(JSONObject.of("eventType", LifeEventType.STOP, "memo", memo).toString());
        appEventService.report(appEvent);
        return respMsg;
    }

    @Override
    public RespMsg<AppGrayConditionBo> appGrayCondition(String appId) {
        AppGrayConditionBo appGrayConditionBo = new AppGrayConditionBo();

        //服务路由策略
        NamespaceBO namespaceBO = configService.findNamespace(appId, GovernanceConstants.DEFAULT_CLUSTER, GovernanceConstants.GOVERNANCE_CONFIG_NAMESPACE);
        List<ItemBO> items = List.of();
        if (namespaceBO != null) {
            items = namespaceBO.getItems();
        }
        items.stream().filter(item -> item.getItem().getKey().equals(DISCOVERY_APP_STRATEGY)).findFirst().ifPresentOrElse(itemBO -> appGrayConditionBo.setDiscovery(itemBO.getItem().getValue()), () -> {
            NamespaceBO namespaceBO1 = configService.findNamespace(GovernanceTransform.GOVERNANCE.getAppId().get(), GovernanceConstants.DEFAULT_CLUSTER, GovernanceTransform.GOVERNANCE.getNamespace());
            namespaceBO1.getItems().stream().filter(itemBO -> itemBO.getItem().getKey().equals(DISCOVERY_GLOBAL_STRATEGY)).findFirst().ifPresentOrElse(itemBO -> appGrayConditionBo.setDiscovery(itemBO.getItem().getValue()),
                    () -> appGrayConditionBo.setDiscovery(DISCOVERY_STRATEGY_NODE));
        });

        //是否有指定域名配置信息
        AppMetadata appMetadata = appMetadataMapper.selectLastByAppId(appId);
        if (appMetadata != null) {
            appGrayConditionBo.setAppIdHost(MapUtil.builder(Convert.toMap(String.class, Object.class, appMetadata.getRpcConfig().getOrDefault("hostPort", MapUtil.newHashMap())))
                    .putAll(Convert.toMap(String.class, Object.class, appMetadata.getRpcConfig().getOrDefault("hostPorts", MapUtil.newHashMap()))).build());
        }
        //是否存在SOA实例在consul中
        appGrayConditionBo.setHealthInstances(rpcRegistryService.healthInstances(appId, List.of("service")).getData());

        //获取上报SOA版本信息
        AppVersion appVersion = appVersionMapper.selectByAppId(appId);
        if (appVersion != null) {
            appGrayConditionBo.setVersion(appVersion.getVersion().get(LALA_BOOT_STARTER_SOA_KEY));
        }
        return RespMsg.respSuc(appGrayConditionBo);
    }

    @Override
    public RespMsg<AppSoaInfo> appSoaInfo(String appId) {
        AppSoaInfo appSoaInfo = new AppSoaInfo();
        AppBaseInfo baseInfo = cmdbFacadeService.baseInfo(appId);
        //识别是否是soa服务
        appSoaInfo.setLang(baseInfo.getTypeStr());

        AppVersion appVersion = appVersionMapper.selectByAppId(appId);
        // 找不到版本列表
        if (appVersion == null) {
            return RespMsg.respSuc(appSoaInfo);
        }

        Map<String, String> version = appVersion.getVersion();
        String soaKey = appSoaInfo.java() ? LALA_BOOT_STARTER_SOA_KEY : PHP_SOA_KEY;
        Optional<String> soaVersionOptional = version.entrySet().stream().filter(entry -> entry.getKey().startsWith(soaKey)).map(Map.Entry::getValue).findFirst();
        // 找不到soa版本
        if (soaVersionOptional.isEmpty()) {
            return RespMsg.respSuc(appSoaInfo);
        }
        //只有找到版本才认为接入
        appSoaInfo.setAccess(true);
        appSoaInfo.setVersion(soaVersionOptional.get());
        return RespMsg.respSuc(appSoaInfo);
    }

    @Override
    public RespMsg<List<AppGroupInfoBo>> appGroupInfo(String appId) {
        RespMsg<List<HealthInstanceBo>> instances = rpcRegistryService.healthInstances(appId, this::soaTagPredicate);
        List<AppGroupInfoBo> groupInfoBos = instances.getData().stream()
                .map(instance -> CommandUtils.toTagsMap(instance.getTags()).getOrDefault("hll.group", StrUtil.EMPTY))
                .map(groupId -> new AppGroupInfoBo(groupId, groupId)).collect(Collectors.toList());
        return RespMsg.respSuc(groupInfoBos);
    }

    @Override
    public RespMsg<Map<String, List<InstanceBo>>> appGroupInstance(String appId, List<String> groupIds, boolean passing) {
        List<InstanceBo> instances;
        if (passing){
            instances = rpcRegistryService.healthInstances(appId,this::soaTagPredicate).getData().stream().map(healthInstanceBo -> BeanUtil.copyProperties(healthInstanceBo,InstanceBo.class)).toList();
        }else {
            instances = rpcRegistryService.allInstances(appId, this::soaTagPredicate).getData();
        }
        Map<String, List<InstanceBo>> allGroupToInstance = instances.stream()
                .collect(Collectors.groupingBy(instanceBo -> CommandUtils.toTagsMap(instanceBo.getTags()).getOrDefault("hll.group", ""),
                Collectors.mapping(instanceBo -> instanceBo, Collectors.toList())));

        Set<String> instanceIds = instances.stream().map(HealthInstanceBo::getId).collect(Collectors.toSet());

        // DB下线列表
        List<AppRegistry> appRegistries = appRegistryMapper.selectByAppId(appId);
        if (CollUtil.isNotEmpty(appRegistries)) {
            // 已经发布的节点包括容器
            final List<String> deployIp = appHybridInstanceId(appId);
            appRegistries.stream().filter(appRegistry -> {
                // 不包含则标记为删除且返回过滤
                if (Objects.nonNull(deployIp) && !deployIp.contains(appRegistry.getInstanceId())) {
                    appRegistryMapper.deleteByInstanceId(appRegistry.getInstanceId());
                    return false;
                }
                return true;
            }).forEach(appRegistry -> {
                AppInstance appInstance = appRegistry.getAppVo();
                if (!instanceIds.contains(appInstance.getInstanceId())){
                    String groupId = appInstance.getCustomTags().getOrDefault("hll.group", "");
                    InstanceBo instanceBo = BeanUtil.copyProperties(appInstance, InstanceBo.class,"status");
                    instanceBo.setId(appInstance.getInstanceId());
                    instanceBo.setStatus(InstanceStatus.OFFLINE);
                    instanceBo.setTags(CommandUtils.toTagsList(appInstance.getCustomTags()));
                    allGroupToInstance.computeIfAbsent(groupId, s -> new ArrayList<>()).add(instanceBo);
                }
            });
        }
        if (CollUtil.isEmpty(groupIds)) {
            return RespMsg.respSuc(allGroupToInstance);
        }
        Map<String, List<InstanceBo>> groupInstances = groupIds.stream()
                .collect(Collectors.toMap(groupId -> groupId, groupId -> allGroupToInstance.getOrDefault(groupId, new ArrayList<>())));
        return RespMsg.respSuc(groupInstances);
    }

    @Override
    public RespMsg<Map<String, Object>> appEndpointMeta(String appId, String instanceId, String userId, MetadataType metadataType, boolean refresh) {
        if (refresh) {
            Map<String, Object> metadata = metadataService.appRemoteEndpointMeta(appId, instanceId, userId, metadataType, -1);
            return RespMsg.respSuc(metadata);
        }
        AppRuntimeMetadata appRuntimeMetadata = appRuntimeMetadataMapper.selectLatest(instanceId, metadataType);
        if (Objects.isNull(appRuntimeMetadata)) {
            Map<String, Object> metadata = metadataService.appRemoteEndpointMeta(appId, instanceId, userId, metadataType, -1);
            return RespMsg.respSuc(metadata);
        }
        return RespMsg.respSuc(appRuntimeMetadata.getMetadata());
    }

    /**
     * 直接相等/注册的instanceId替换.为- (兼容PHP注册信息)
     */
    private Optional<HealthInstanceBo> findMatchFirst(List<HealthInstanceBo> healthInstanceBos, String instanceId) {
        return healthInstanceBos.stream().filter(v -> v.getId().equals(instanceId)
                || StrUtil.replace(v.getId(), ".", "-").equals(instanceId)
        ).findFirst();
    }

    private List<String> appHybridInstanceId(String appId) {
        final List<String> hybridInstanceId = new ArrayList<>();
        try {
            List<PodInfo> appPods = loneFacadeService.getAppPods(appId, true, true);
            ofNullable(appPods).ifPresent(podInfos -> podInfos.forEach(podInfo -> hybridInstanceId.add(podInfo.getIp())));
            List<EcsInfo> ecsInfos = cmdbFacadeService.appResource(appId, "0", 1);
            ofNullable(ecsInfos).ifPresent(ecs -> ecs.forEach(ecsInfo -> hybridInstanceId.add(ecsInfo.getLanIp())));
        } catch (LoneException | CmdbException e) {
            return hybridInstanceId;
        }
        return hybridInstanceId.stream().map(ip -> appId + "-" + StrUtil.replace(ip, ".", "-")).collect(Collectors.toList());
    }


    private RespMsg<Void> versionCheck(HealthInstanceBo healthInstanceBo) {
        String version = null;
        out:
        for (String tag : healthInstanceBo.getTags()) {
            if (!tag.contains("=") && tag.contains(StrUtil.DOT)) {
                for (String match : VERSIONS) {
                    if (StrUtil.containsAnyIgnoreCase(tag, match)) {
                        version = tag;
                        break out;
                    }
                }
            }
        }
        if (StrUtil.isEmpty(version)) {
            return RespMsg.respErr("未知版本");
        }
        //1.2.6
        List<String> vp = StrUtil.split(version, StrUtil.DOT);
        String joinVersion = StrUtil.join("", vp); //126xxxx  126-xxxx

        StringBuilder versionNum = new StringBuilder();
        for (int i = 0; i < joinVersion.length(); i++) {
            if (joinVersion.charAt(i) >= 48 && joinVersion.charAt(i) <= 57) {
                versionNum.append(joinVersion.charAt(i));
            } else {
                break;
            }
        }
        String num = versionNum.toString();
        if (StrUtil.isEmpty(num)) {
            return RespMsg.respErr(StrUtil.format("未知版本,{}", version));
        }
        if (Integer.parseInt(num) < 126) {
            return RespMsg.respErr("SOA版本低于1.2.6,暂不支持服务上下线操作");
        }
        return null;
    }

    private boolean soaTagPredicate(String tag) {
        return Stream.of("service", "service=grpc").anyMatch(s -> StrUtil.equals(s, tag));
    }

}
