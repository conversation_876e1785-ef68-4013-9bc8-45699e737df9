package cn.huolala.arch.hermes.mesh.sentinel.altert;

import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiLimitConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.ApiTimeoutConfigBo;
import cn.huolala.arch.hermes.mesh.api.bos.panel.AreaEnvInfoBo;
import cn.huolala.arch.hermes.mesh.common.enums.AlertEventType;
import cn.huolala.arch.hermes.mesh.component.lark.enums.MsgCardLevel;
import cn.huolala.arch.hermes.mesh.component.lark.model.message.MsgCardModel;
import cn.huolala.arch.hermes.mesh.component.lark.notify.aspect.LarkNotifyAspectService;
import cn.huolala.arch.hermes.mesh.component.lark.utils.MessageUtil;
import cn.huolala.arch.hermes.mesh.sentinel.altert.annotation.LarkNotifyData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.AppAlertDataObject;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.early_warning_tips.RateLimitEarlyTipsData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.early_warning_tips.TimeoutEarlyTipsData;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.inspection.InspectionWarningData;
import cn.huolala.arch.hermes.mesh.sentinel.config.HermesPanelConfig;
import cn.huolala.arch.hermes.mesh.sentinel.service.AlertService;
import cn.huolala.arch.hermes.mesh.sentinel.service.PanelService;
import cn.huolala.arch.hermes.mesh.stream.application.event.EarlyWarningCronResultEvent;
import cn.huolala.arch.hermes.mesh.stream.application.event.LimitEarlyWarningCronResultEvent;
import cn.huolala.arch.hermes.mesh.stream.application.event.TimeoutEarlyWarningCronResultEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.mesh.sentinel.altert.AlertConstant.PANEL_DOMAIN;


@Slf4j
@Component
public class AppAlertEventListener {

    @Resource
    private AlertService alertService;

    @Resource
    private PanelService panelService;

    @Resource
    private HermesPanelConfig hermesPanelConfig;

    @Resource
    private LarkNotifyAspectService larkNotifyAspectService;


    @Async
    @EventListener
    public void alertEventListener(AppAlertEvent appAlertEvent) {
        try {
            log.info("handle alertEvent: {}", appAlertEvent.getSource().getClass().getSimpleName());
            final LinkedHashMap<String, String> data = new LinkedHashMap<>();
            final Map<String, String> urlData = new LinkedHashMap<>();
            AppAlertDataObject alertDataObject = (AppAlertDataObject) appAlertEvent.getSource();
            LarkNotifyData larkNotify = alertDataObject.getClass().getAnnotation(LarkNotifyData.class);
            Assert.isTrue(larkNotify != null, "AppAlertEvent 内的source对象一定需要注解 LarkNotifyData");

            AlertEventType alertEventType = larkNotify.type();
            //TODO 重连事件去除，后续支持
            if (alertEventType.equals(AlertEventType.RECONNECTION_EVENT)) {
                return;
            }
            urlData.put(PANEL_DOMAIN, hermesPanelConfig.getAddress());
            final List<String> userName = new ArrayList<>(Arrays.stream(larkNotify.users()).toList());
            final List<String> groupName = new ArrayList<>(Arrays.stream(larkNotify.groups()).toList());

            larkNotifyAspectService.handleObjectField(alertDataObject, data, urlData, userName, groupName);
            String clickUrl = larkNotifyAspectService.handleLarkMsgBottomUrl(larkNotify.clickUrl(), urlData);
            MsgCardModel msgCardModel = MessageUtil.assembleMessageCard(alertEventType.getTitle(), data, MsgCardLevel.valueOf(alertEventType.getDefaultLevel().name()), clickUrl);

            AlertMessage alertMessage = new AlertMessage();
            alertMessage.setCardData(data);
            alertMessage.setMsgCardModel(msgCardModel);

            // 全局通知在这里做判断
            if (!panelEnvAlert()) {
                return;
            }
            alertService.notifyWithAlertRule(alertDataObject.getAppId(), alertEventType, alertMessage);
        } catch (Exception e) {
            log.warn("handle alert event fail: {}", appAlertEvent,e);
        }
    }

    /**
     * 监听来自组件质检事件的告警
     */
    @Async
    @EventListener
    public void alertEventListener(InspectionWarningData inspectionWarningData) {
        try {
            log.info("handle InspectionWarningData event: componentName={}, content={}",
                    inspectionWarningData.getComponentName(), inspectionWarningData.getContent());

            if (!panelEnvAlert()) {
                return;
            }

            // 获取注解信息
            LarkNotifyData larkNotify = inspectionWarningData.getClass().getAnnotation(LarkNotifyData.class);
            Assert.isTrue(larkNotify != null, "InspectionWarningData 需要注解 LarkNotifyData");

            AlertEventType alertEventType = larkNotify.type();

            final LinkedHashMap<String, String> data = new LinkedHashMap<>();
            final Map<String, String> urlData = new LinkedHashMap<>();
            final List<String> userName = new ArrayList<>(Arrays.stream(larkNotify.users()).toList());
            final List<String> groupName = new ArrayList<>(Arrays.stream(larkNotify.groups()).toList());

            urlData.put(PANEL_DOMAIN, hermesPanelConfig.getAddress());

            // 处理对象字段，提取用户和群组信息
            larkNotifyAspectService.handleObjectField(inspectionWarningData, data, urlData, userName, groupName);
            String clickUrl = larkNotifyAspectService.handleLarkMsgBottomUrl(larkNotify.clickUrl(), urlData);

            log.info("InspectionWarningDataListener groupName: {}, userName: {}", groupName, userName);
            // 简单通知，直接使用从 InspectionWarningData 中提取的用户和群组信息
            alertService.simpleNotify(groupName, userName, alertEventType.getTitle(), data,
                    MsgCardLevel.valueOf(alertEventType.getDefaultLevel().name()), clickUrl);

            log.info("InspectionWarningData notification sent successfully for component: {}",
                    inspectionWarningData.getComponentName());

        } catch (Exception e) {
            log.warn("handle InspectionWarningData event fail: {}", inspectionWarningData, e);
        }
    }

    /**
     * 监听来自Stream 模块定时任务跑的结果的事件
     */
    @Async
    @EventListener
    public void alertEventListener(EarlyWarningCronResultEvent earlyWarningCronResultEvent) {
        String appId = (String) earlyWarningCronResultEvent.getSource();
        if (earlyWarningCronResultEvent instanceof LimitEarlyWarningCronResultEvent) {
            LimitEarlyWarningCronResultEvent resultEvent = (LimitEarlyWarningCronResultEvent) earlyWarningCronResultEvent;
            this.alertEventListener(new AppAlertEvent(RateLimitEarlyTipsData.builder()
                    .appId(appId)
                    .areaEnvId(hermesPanelConfig.getAreaEnvId())
                    .cluster(resultEvent.getCluster())
                    .commandKeys(resultEvent.getExceededThreshold().keySet().stream().map(ApiLimitConfigBo::getName).collect(Collectors.toList()))
                    .apiType(resultEvent.getApiType().name().toLowerCase())
                    .globalEnable(panelEnvAlert())
                    .build()));
        } else if (earlyWarningCronResultEvent instanceof TimeoutEarlyWarningCronResultEvent) {
            TimeoutEarlyWarningCronResultEvent resultEvent = (TimeoutEarlyWarningCronResultEvent) earlyWarningCronResultEvent;
            this.alertEventListener(new AppAlertEvent(TimeoutEarlyTipsData.builder()
                    .appId(appId)
                    .areaEnvId(hermesPanelConfig.getAreaEnvId())
                    .cluster(resultEvent.getCluster())
                    .commandKeys(resultEvent.getExceededThreshold().keySet().stream().map(ApiTimeoutConfigBo::getName).collect(Collectors.toList()))
                    .globalEnable(panelEnvAlert())
                    .build()));
        }
    }

    private boolean panelEnvAlert() {
        AreaEnvInfoBo envInfo = panelService.getEnvInfo();
        return Optional.ofNullable(envInfo).map(AreaEnvInfoBo::isAlert).orElse(false);
    }

}
