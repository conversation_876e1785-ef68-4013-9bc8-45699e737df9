package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.api.config.DiscoveryConfig;
import cn.huolala.arch.hermes.config.spring.boot.ConfigProperties;
import cn.huolala.arch.hermes.mesh.api.RpcRegistryService;
import cn.huolala.arch.hermes.mesh.api.bos.metadata.ApiCommandKeyBo;
import cn.huolala.arch.hermes.mesh.api.bos.registry.HealthInstanceBo;
import cn.huolala.arch.hermes.mesh.common.enums.ApiProtocol;
import cn.huolala.arch.hermes.mesh.common.enums.ApiType;
import cn.huolala.arch.hermes.mesh.common.enums.MetadataType;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.utils.InstanceUtils;
import cn.huolala.arch.hermes.mesh.common.utils.JacksonUtils;
import cn.huolala.arch.hermes.mesh.dao.entity.ApiCommandKey;
import cn.huolala.arch.hermes.mesh.dao.entity.ApiMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppRuntimeMetadata;
import cn.huolala.arch.hermes.mesh.dao.entity.AppVersion;
import cn.huolala.arch.hermes.mesh.dao.mapper.ApiCommandKeyMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.ApiMetadataMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppMetadataMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppRuntimeMetadataMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.AppVersionMapper;
import cn.huolala.arch.hermes.mesh.rpc.RpcInvokerContext;
import cn.huolala.arch.hermes.mesh.sentinel.config.EndpointAuthBean;
import cn.huolala.arch.hermes.mesh.sentinel.service.GovernancePromoter;
import cn.huolala.arch.hermes.mesh.sentinel.service.MetadataService;
import cn.huolala.arch.hermes.mesh.sentinel.service.rpc.AbstractActuatorService;
import cn.huolala.arch.hermes.mesh.sentinel.service.rpc.RpcActuatorService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MetadataServiceImpl extends AbstractActuatorService implements MetadataService {

    @Resource
    private ApiMetadataMapper apiMetadataMapper;

    @Resource
    private AppMetadataMapper appMetadataMapper;

    @Resource
    private ApiCommandKeyMapper apiCommandKeyMapper;

    @Resource
    private AppVersionMapper appVersionMapper;

    @Resource
    private GovernancePromoter governancePromoter;

    @Resource
    private RpcRegistryService rpcRegistryService;

    @Resource
    private RpcActuatorService rpcActuatorService;

    @Resource
    private AppRuntimeMetadataMapper appRuntimeMetadataMapper;

    @Resource
    private EndpointAuthBean endpointAuthBean;

    @Override
    public AppMetadata selectLastByAppId(String appId) {
        return appMetadataMapper.selectLastByAppId(appId);
    }

    @Override
    public void report(String appId, String instanceId, JsonNode jsonNode) {
        JsonNode appJsonNode = jsonNode.get("app");
        if (appJsonNode != null && !appJsonNode.isNull()) {
            AppMetadata appMetadata = JacksonUtils.serialize(appJsonNode, AppMetadata.class);
            appMetadata.setAppId(appId);
            appMetadata.setInstanceId(instanceId);
            appMetadataMapper.insert(appMetadata);
            addReleaseTrafficReasonable(appId, instanceId, appMetadata.getRpcConfig());
        }

        final Map<ApiType, Map<String, ApiMetadata.ApiDefinitionMetadata>> collMetadata = new HashMap<>();
        processApiMetadata(collMetadata, jsonNode.get("api"), ApiProtocol.JSON_RPC);
        processApiMetadata(collMetadata, jsonNode.get("apiGrpc"), ApiProtocol.GRPC);
        collMetadata.forEach((apiType, metadataMap) -> {
            saveApiMetadata(appId, apiType, instanceId, metadataMap);
            /*
             * 保存到api_command_key 如果存在  覆盖不存在则插入 存在则不插入
             */
            if (apiType == ApiType.PROVIDER) {
                saveApiCommandKey(appId, apiType, metadataMap);
            }
            if (apiType == ApiType.CONSUMER) {
                saveApiCommandKey(appId, apiType, metadataMap);
            }
        });

    }

    private void processApiMetadata(Map<ApiType, Map<String, ApiMetadata.ApiDefinitionMetadata>> collMetadata, JsonNode apiJsonNode, ApiProtocol apiProtocol) {
        if (Objects.isNull(apiJsonNode)) {
            return;
        }
        Iterator<Map.Entry<String, JsonNode>> fields = apiJsonNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> jsonNodeEntry = fields.next();
            String key = jsonNodeEntry.getKey();
            ApiType apiType = ApiType.covert(key);
            if (apiType != null) {
                JsonNode value = jsonNodeEntry.getValue();
                Map<String, ApiMetadata.ApiDefinitionMetadata> apiMetadataMap = JacksonUtils.serialize(value, new TypeReference<>() {
                });
                apiMetadataMap.values().forEach(apiDefinitionMetadata -> {
                    apiDefinitionMetadata.setProtocol(apiProtocol);
                    //GRPC WILL BE
                    apiDefinitionMetadata.setServiceName(StrUtil.removeSuffix(apiDefinitionMetadata.getServiceName(), "Service$Interface"));
                });
                collMetadata.computeIfAbsent(apiType, type -> new HashMap<>()).putAll(apiMetadataMap);
            }
        }
    }


    private void saveApiMetadata(String appId, ApiType apiType, String instanceId, Map<String, ApiMetadata.ApiDefinitionMetadata> apiMetadataMap) {
        ApiMetadata apiMetadata = new ApiMetadata();
        apiMetadata.setApiType(apiType);
        apiMetadata.setAppId(appId);
        apiMetadata.setInstanceId(instanceId);
        apiMetadata.setMetadata(apiMetadataMap);
        apiMetadataMapper.insert(apiMetadata);
    }



    private void saveApiCommandKey(String appId, ApiType apiType, Map<String, ApiMetadata.ApiDefinitionMetadata> apiMetadataMap) {
        ApiCommandKey apiCommandKey = apiCommandKeyMapper.find(appId, apiType);
        if (apiCommandKey == null) {
            apiCommandKey = new ApiCommandKey();
            apiCommandKey.setAppId(appId);
            apiCommandKey.setApiType(apiType);
            apiCommandKey.setCommandKey(new ArrayList<>());
        }
        List<ApiCommandKey.CommandKey> commandKeys = apiCommandKey.getCommandKey();
        Map<String, ApiCommandKey.CommandKey> commandKeyMap = commandKeys.stream().collect(Collectors.toMap(ApiCommandKey.CommandKey::getName, v -> v));
        for (ApiMetadata.ApiDefinitionMetadata apiDefinitionMetadata : apiMetadataMap.values()) {
            List<ApiMetadata.MethodDefinitionMetadata> methodDefinitionMetadata = apiDefinitionMetadata.getMethodDefinitionMetadata();
            for (ApiMetadata.MethodDefinitionMetadata mmt : methodDefinitionMetadata) {
                String commandKey = mmt.getCommandKey();
                ApiCommandKey.CommandKey ck = new ApiCommandKey.CommandKey();
                ck.setName(commandKey);
                ck.setServiceName(apiDefinitionMetadata.getServiceName());
                ck.setServicePath(apiDefinitionMetadata.getServicePath());
                ck.setMethodName(mmt.getMethodName());
                ck.setMethodPath(mmt.getMethodPath());
                ck.setProtocol(apiDefinitionMetadata.getProtocol());
                commandKeyMap.put(commandKey, ck);
            }
        }
        apiCommandKey.setCommandKey(new ArrayList<>(commandKeyMap.values()));
        if (apiCommandKey.getId() == null) {
            apiCommandKeyMapper.insert(apiCommandKey);
        } else {
            apiCommandKeyMapper.updateById(apiCommandKey);
        }
    }


    @Override
    public void dynamic(String appId, String instanceId, JsonNode jsonNode) {
        ApiMetadata apiMetadata = apiMetadataMapper.findByInstanceId(instanceId, ApiType.DYNAMIC);
        Map<String, ApiMetadata.ApiDefinitionMetadata> apiMetadataMap = JacksonUtils.serialize(jsonNode, new TypeReference<Map<String, ApiMetadata.ApiDefinitionMetadata>>() {
        });
        apiMetadataMap.values().forEach(apiDefinitionMetadata -> apiDefinitionMetadata.setProtocol(ApiProtocol.GENERIC));
        if (apiMetadata == null) {
            apiMetadata = new ApiMetadata();
            apiMetadata.setApiType(ApiType.DYNAMIC);
            apiMetadata.setAppId(appId);
            apiMetadata.setInstanceId(instanceId);
            apiMetadata.setMetadata(apiMetadataMap);
            apiMetadataMapper.insert(apiMetadata);
            return;
        }
        apiMetadata.getMetadata().putAll(apiMetadataMap);
        apiMetadataMapper.updateById(apiMetadata);
    }


    @Override
    public void commandKey(String appId, List<ApiCommandKey.CommandKey> apiCommandKeys, ApiType type) {
        ApiCommandKey apiCommandKey = apiCommandKeyMapper.find(appId, type);
        if (apiCommandKey == null) {
            apiCommandKey = new ApiCommandKey();
            apiCommandKey.setAppId(appId);
            apiCommandKey.setApiType(type);
            apiCommandKey.setCommandKey(new ArrayList<>());
        }

        List<ApiCommandKey.CommandKey> exitCommandKeys = apiCommandKey.getCommandKey();
        Map<String, ApiCommandKey.CommandKey> commandKeyMap = exitCommandKeys.stream().collect(Collectors.toMap(ApiCommandKey.CommandKey::getName, v -> v));

        for (ApiCommandKey.CommandKey apiCommandKeyCK : apiCommandKeys) {
            String name = apiCommandKeyCK.getName();
            //TODO referenceBuilder 目前上报的信息无法支持协议
            apiCommandKeyCK.setProtocol(ApiProtocol.JSON_RPC);
            // 接口的协议基本都是通过ApiMetadata去生成的,后面为了兼容上报的就无需覆盖
            commandKeyMap.putIfAbsent(name, apiCommandKeyCK);
        }

        apiCommandKey.setCommandKey(new ArrayList<>(commandKeyMap.values()));
        if (apiCommandKey.getId() == null) {
            apiCommandKeyMapper.insert(apiCommandKey);
        } else {
            apiCommandKeyMapper.updateById(apiCommandKey);
        }
    }

    @Override
    public void version(String appId, String instanceId, Map<String, String> version) {
        AppVersion appVersion = new AppVersion();
        appVersion.setAppId(appId);
        appVersion.setInstanceId(instanceId);
        appVersion.setVersion(version);
        appVersionMapper.insert(appVersion);
    }

    @Override
    public List<ApiCommandKeyBo> findCommandKey(String appId, ApiType apiType) {
        ApiCommandKey apiCommandKey = apiCommandKeyMapper.find(appId, apiType);
        if (apiCommandKey == null) {
            return List.of();
        }
        List<ApiCommandKeyBo> result = apiCommandKey.getCommandKey().stream().map(commandKey -> {
            ApiCommandKeyBo apiCommandKeyBo = BeanUtil.copyProperties(commandKey, ApiCommandKeyBo.class);
            if (ApiType.CONSUMER == apiType) {
                String name = commandKey.getName();
                //jsonrpc-provider-svc@/params/queryObj
                if (name.contains("@/")) {
                    name = StrUtil.split(name, "@/").get(0);
                } else {
                    //jsonrpc-provider-svc@params/queryObj
                    name = StrUtil.split(name, "@").get(0);
                }
                apiCommandKeyBo.setAppId(name);
            } else {
                apiCommandKeyBo.setAppId(appId);
            }
            return apiCommandKeyBo;
        }).toList();

        if (apiType == ApiType.PROVIDER){
            //result 结果集根据serviceName，methoName，servicePath，methodPath相加的结果作为唯一条件去重
            return result.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(ApiCommandKeyBo::getServiceName).thenComparing(ApiCommandKeyBo::getMethodName).thenComparing(ApiCommandKeyBo::getServicePath).thenComparing(ApiCommandKeyBo::getMethodPath))), ArrayList::new));
        }
        return result;
    }


    /**
     * 触发K8s放量检测
     */
    private void addReleaseTrafficReasonable(String appId, String instanceId, Map<String, Object> rpcConfig) {
        ConfigProperties configProperties = JacksonUtils.serialize(rpcConfig, ConfigProperties.class);
        long startTime = System.currentTimeMillis();
        DiscoveryConfig discovery = configProperties.getDiscovery();
        if (Objects.nonNull(discovery)) {
            startTime = Long.parseLong(Optional.ofNullable(discovery.getTags()).orElse(new HashMap<>()).getOrDefault("startTime", String.valueOf(System.currentTimeMillis())));
        }
        governancePromoter.releaseTrafficReasonable(appId, InstanceUtils.ipFromInstance(instanceId), startTime);
    }

    public Map<String, Object> appRemoteEndpointMeta(String appId, String instanceId, String userId, MetadataType metadataType, long metaDataId) {

        RespMsg<HealthInstanceBo> instances = rpcRegistryService.healthInstances(appId, instanceId, s -> s.equals("management"));
        HealthInstanceBo managementInstances = instances.getData();
        String host = managementInstances.getHost();

        //2.确认调用端口
        Optional<Integer> port = confirmMonitorPort(appId, host, instanceId, List.of(managementInstances));
        if (port.isEmpty()) {
            throw new RuntimeException("无法确定"+appId+"暴露的endpoint端口/历史实例已重启或下线");
        }

        RpcInvokerContext.getContext().setHost(host);
        RpcInvokerContext.getContext().setPort(port.get());
        endpointAuthBean.setEndpointAuthSecret(appId, "default");

        Map<String, Object> metadata = rpcActuatorService.hermesMetadata(metadataType.getPath());
        AppRuntimeMetadata appRuntimeMetadata = new AppRuntimeMetadata();
        appRuntimeMetadata.setAppId(appId);
        appRuntimeMetadata.setInstanceId(instanceId);
        appRuntimeMetadata.setUserId(userId);
        appRuntimeMetadata.setMetadata(metadata);
        appRuntimeMetadata.setLatest(true);
        appRuntimeMetadata.setType(metadataType);
        if (metaDataId >= 0) {
            appRuntimeMetadata.setContactAppMetaId(metaDataId);
        }
        appRuntimeMetadataMapper.updateLatestToFalseByInstanceIdAndType(instanceId, metadataType);
        appRuntimeMetadataMapper.insert(appRuntimeMetadata);
        return metadata;
    }

}
