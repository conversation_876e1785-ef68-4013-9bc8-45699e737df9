package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.mesh.common.GlobalConstants;
import cn.huolala.arch.hermes.mesh.common.enums.AlertEventType;
import cn.huolala.arch.hermes.mesh.common.enums.AlertLevel;
import cn.huolala.arch.hermes.mesh.common.utils.JacksonUtils;
import cn.huolala.arch.hermes.mesh.component.cmdb.model.AppBaseInfo;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.component.lark.enums.MsgCardLevel;
import cn.huolala.arch.hermes.mesh.component.lark.enums.SendType;
import cn.huolala.arch.hermes.mesh.component.lark.enums.UrgentType;
import cn.huolala.arch.hermes.mesh.component.lark.model.group.GroupInfo;
import cn.huolala.arch.hermes.mesh.component.lark.model.message.MsgCardModel;
import cn.huolala.arch.hermes.mesh.component.lark.model.message.SendMessageResp;
import cn.huolala.arch.hermes.mesh.component.lark.service.LarkFacadeService;
import cn.huolala.arch.hermes.mesh.component.lark.utils.MessageUtil;
import cn.huolala.arch.hermes.mesh.dao.entity.alert.AlertMessageLog;
import cn.huolala.arch.hermes.mesh.dao.entity.alert.AlertRule;
import cn.huolala.arch.hermes.mesh.dao.mapper.alert.AlertMessageLogMapper;
import cn.huolala.arch.hermes.mesh.dao.mapper.alert.AlertRuleMapper;
import cn.huolala.arch.hermes.mesh.sentinel.altert.AlertMessage;
import cn.huolala.arch.hermes.mesh.sentinel.service.AlertService;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlertServiceImpl implements AlertService {

    @Resource
    private AlertRuleMapper alertRuleMapper;

    @Resource
    private AlertMessageLogMapper alertMessageLogMapper;

    @Resource
    private LarkFacadeService larkFacadeService;

    @Resource
    private CmdbFacadeService cmdbFacadeService;


    @Override
    public void notifyWithAlertRule(String appId, AlertEventType alertEventType, AlertMessage alertMessage) {

        final AlertRule alertRule = appAlertRule(appId, alertEventType);
        AlertLevel alertLevel = alertRule.getLevel();
        Map<String, String> notifyUserNameAndId = new HashMap<>();
        //获取飞书信息
        alertRule.getAlertUser().forEach(eName -> {
            String openId = larkFacadeService.getOpenIdByEName(eName);
            if (StrUtil.isNotEmpty(openId)) {
                notifyUserNameAndId.put(eName, openId);
            }
        });

        //私聊通知
        notifyUserNameAndId.keySet().forEach(name -> {
            String userOpenId = notifyUserNameAndId.get(name);
            SendMessageResp sendMessageResp = larkFacadeService.sendInteractiveMessage(SendType.PRIVATE_CHAT, userOpenId, alertMessage.getMsgCardModel());
            log.info("send message to user : {}", name);
            //TOTO 应用级别加急
            if (alertLevel == AlertLevel.ERROR && sendMessageResp != null) {
                larkFacadeService.urgentMessage(UrgentType.APP, sendMessageResp.getMessageId(), ListUtil.toList(userOpenId));
            }
        });

        //群组通知
        Map<String, String> notifyGroupNameAndId = new HashMap<>();
        Map<String, GroupInfo> groupInfoMap = larkFacadeService.groupWithRobot();
        alertRule.getAlertGroup().forEach(groupName -> Optional.ofNullable(groupInfoMap.get(groupName)).ifPresent(groupInfo -> notifyGroupNameAndId.put(groupName, groupInfo.getChatId())));
        notifyGroupNameAndId.forEach((groupName, chatId) -> {
            log.info("send message to groups : {}", groupName);
            larkFacadeService.sendInteractiveMessage(SendType.GROUP_CHAT, chatId, alertMessage.getMsgCardModel());
        });

        //记录发送消息日志
        AlertMessageLog alertMessageLog = new AlertMessageLog();
        alertMessageLog.setAppId(appId);
        alertMessageLog.setMessage(JacksonUtils.serialize(alertMessage.getMsgCardModel()));
        alertMessageLog.setAlertGroup(new HashMap<>());
        alertMessageLog.setAlertUser(notifyUserNameAndId);
        alertMessageLog.setLevel(alertLevel);
        alertMessageLog.setAlertType(alertEventType);
        alertMessageLog.setMessageContent(alertMessage.getCardData());
        alertMessageLog.setAck(alertEventType.getDefaultAck());
        alertMessageLogMapper.insert(alertMessageLog);
    }

    @Override
    public void simpleNotify(List<String> specifyGroupName, List<String> specifyUserName, String tile, LinkedHashMap<String, String> data, MsgCardLevel level, String url) {
        MsgCardModel msgCardModel = MessageUtil.assembleMessageCard(tile, data, MsgCardLevel.INFO, url);
        final List<String> userOpenIds = new ArrayList<>();
        final List<String> groupIds = new ArrayList<>();
        Optional.ofNullable(specifyUserName).ifPresent(userNames -> userNames.forEach(eName -> {
            Optional.ofNullable(larkFacadeService.getOpenIdByEName(eName)).filter(StrUtil::isNotBlank).ifPresent(userOpenIds::add);
        }));
        Map<String, GroupInfo> groupInfoMap = larkFacadeService.groupWithRobot();
        log.info("simpleNotify groupInfoMap : {}", groupInfoMap);
        Optional.ofNullable(specifyGroupName).ifPresent(groups -> groups.forEach(groupName
                -> Optional.ofNullable(groupInfoMap.get(groupName)).ifPresent(groupInfo -> groupIds.add(groupInfo.getChatId()))));
        for (String userOpenId : userOpenIds) {
            larkFacadeService.sendInteractiveMessage(SendType.PRIVATE_CHAT, userOpenId, msgCardModel);
        }
        for (String groupId : groupIds) {
            larkFacadeService.sendInteractiveMessage(SendType.GROUP_CHAT, groupId, msgCardModel);
        }
    }

    @Override
    public void simpleNotify(String appId, String tile, LinkedHashMap<String, String> data, MsgCardLevel level, String url) {
        AppBaseInfo appBaseInfo = cmdbFacadeService.baseInfo(appId);
        if (Objects.isNull(appBaseInfo)) {
            return;
        }
        List<String> alertUser = StrUtil.split(appBaseInfo.getMasterDev(), StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).toList();
        simpleNotify(List.of(), alertUser, tile, data, level, url);
    }


    @Override
    public AlertRule appAlertRule(String appId, AlertEventType alertEventType) {
        return Optional.ofNullable(alertRuleMapper.select(appId, alertEventType))
                .orElseGet(() -> defaultAlertRule(appId, alertEventType));
    }


    private AlertRule defaultAlertRule(String appId, AlertEventType alertEventType) {
        AlertRule alertRuleVo = new AlertRule();
        alertRuleVo.setAppId(appId);
        alertRuleVo.setAlertType(alertEventType);
        alertRuleVo.setLevel(alertEventType.getDefaultLevel());

        AppBaseInfo appBaseInfo = cmdbFacadeService.baseInfo(appId);
        List<String> alertUser = StrUtil.split(appBaseInfo.getMasterDev(), StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        alertUser.addAll(StrUtil.split(appBaseInfo.getMasterProduct(), StrUtil.COMMA).stream().filter(StrUtil::isNotBlank).toList());
        alertRuleVo.setAlertUser(alertUser);
        alertRuleVo.setAlertGroup(new ArrayList<>());
        alertRuleVo.setNeedAck(alertEventType.getDefaultAck());
        //获取全局默认通知
        boolean alert = Boolean.parseBoolean(RpcContext.getServerAttachment().getAttachmentString(GlobalConstants.GLOBAL_ALERT, "false"));
        alertRuleVo.setEnabled(alert);
        return alertRuleVo;
    }

}
