package cn.huolala.arch.hermes.mesh.sentinel.service.impl;

import cn.huolala.arch.hermes.mesh.api.bos.PointBo;
import cn.huolala.arch.hermes.mesh.api.bos.SeriesBo;
import cn.huolala.arch.hermes.mesh.common.enums.GovernanceType;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.huolala.arch.hermes.mesh.common.utils.InstanceUtils;
import cn.huolala.arch.hermes.mesh.common.utils.JacksonUtils;
import cn.huolala.arch.hermes.mesh.common.utils.TimeUtils;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.dao.entity.promotion.GovernancePromotionLog;
import cn.huolala.arch.hermes.mesh.dao.mapper.promotion.GovernancePromotionLogMapper;
import cn.huolala.arch.hermes.mesh.monitor.service.AppMetricService;
import cn.huolala.arch.hermes.mesh.monitor.service.rpc.RpcMetricServiceImpl;
import cn.huolala.arch.hermes.mesh.sentinel.altert.data.app.check.AppWarmUpAbnormalData;
import cn.huolala.arch.hermes.mesh.sentinel.promotion.ReleaseTrafficPromotionData;
import cn.huolala.arch.hermes.mesh.sentinel.promotion.WarmUpPromotionData;
import cn.huolala.arch.hermes.mesh.sentinel.queue.QueueContext;
import cn.huolala.arch.hermes.mesh.sentinel.queue.RunnableDelayed;
import cn.huolala.arch.hermes.mesh.sentinel.service.GovernancePromoter;
import cn.huolala.arch.hermes.mesh.sentinel.service.SimpleMessageService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GovernancePromoterImpl implements GovernancePromoter {

    @Resource
    private QueueContext queueContext;

    @Resource
    private RpcMetricServiceImpl rpcMetricService;

    @Resource
    private SimpleMessageService simpleMessageService;

    @Resource
    private AppMetricService appMetricService;

    @Resource
    private CmdbFacadeService facadeService;

    @Resource
    private GovernancePromotionLogMapper governancePromotionLogMapper;

    @Override
    public void warmUpReasonable(String appId, String address, long delayTime, long startTime, long warmUpTime) {
        // 预热只有java服务才有
        if (!facadeService.java(appId)) {
            return;
        }
        // startTime会重置，只在注册就会更新
        // 延迟时间确认
        long delayEndTime = startTime + delayTime;
        long now = System.currentTimeMillis();
        long triggerTime = delayEndTime - now > 0 ? delayEndTime : now;
        queueContext.getQueue().add(new RunnableDelayed("warmUpReasonable", triggerTime, warmUpReasonableRunner(appId, address, delayTime, startTime, warmUpTime)));
    }


    @Override
    public void releaseTrafficReasonable(String appId, String address, long startTime) {
        if (!facadeService.java(appId)) {
            return;
        }
        queueContext.getQueue().add(new RunnableDelayed("releaseTrafficReasonableRunner", startTime + TimeUnit.MINUTES.toMillis(2), releaseTrafficReasonableRunner(appId, address, startTime)));
    }

    private Runnable releaseTrafficReasonableRunner(String appId, String address, long startTime) {
        return () -> {
            try {
                // 启动时间6秒
                long metricStartTime = startTime + 6 * 1000;
                RespMsg<SeriesBo> respMsg = rpcMetricService.podCpuUseMetric(appId, address, metricStartTime, metricStartTime + 60 * 1000 * 2);
                // 1. 不需要一定是这个节点, 一天内有HTTP流量
                RespMsg<List<SeriesBo>> urlApiMetric = rpcMetricService.urlApiProviderSideMetric(appId, null, TimeUtils.todayTimeStampNearby(-1), metricStartTime);

                AtomicBoolean urlApiMetricAllZero = new AtomicBoolean(false);
                if (urlApiMetric.isSuccess()) {
                    boolean allZero = urlApiMetric.getData().stream().allMatch(seriesBo -> seriesBo.getSeries().stream().allMatch(pointBo -> pointBo.getValue() <= 0));
                    urlApiMetricAllZero.set(allZero);
                }
                // urlApi存在流量才记录
                if (urlApiMetricAllZero.get()) {
                    return;
                }
                // 2.RT比较 两倍 && > 1s
                double beforeRT = appMetricService.urlApiProviderSideRT95Metric(appId, null, TimeUtils.timeMillisRange(metricStartTime, -2, TimeUnit.HOURS),
                                TimeUtils.timeMillisRange(metricStartTime, -1, TimeUnit.HOURS))
                        .getSeries().stream().map(PointBo::getValue).collect(Collectors.averagingDouble(Double::doubleValue));

                double currentRT = appMetricService.urlApiProviderSideRT95Metric(appId, address, metricStartTime, metricStartTime + 60 * 1000 * 2)
                        .getSeries().stream().map(PointBo::getValue).filter(d -> d != 0.0).collect(Collectors.averagingDouble(Double::doubleValue));


                if (respMsg.isSuccess()) {
                    List<PointBo> series = respMsg.getData().getSeries();
                    if (CollUtil.isEmpty(series)) {
                        return;
                    }
                    Double[] array = series.stream().map(PointBo::getValue).toList().toArray(new Double[series.size()]);
                    Double max = NumberUtil.max(array);
                    boolean reasonable = max < 70;
                    //不合理再进行插入
                    if (reasonable) {
                        return;
                    }
                    // 3.获取数据保存EX指标
                    Map<String, Double> exData = appMetricService.appExceptionMetric(appId, address, metricStartTime, metricStartTime + 60 * 1000 * 2)
                            .stream().collect(Collectors.toMap(SeriesBo::getName, seriesBo
                                    -> NumberUtil.max(seriesBo.getSeries().stream().map(PointBo::getValue).toList().toArray(new Double[seriesBo.getSeries().size()]))));
                    ReleaseTrafficPromotionData promotionData = new ReleaseTrafficPromotionData();
                    promotionData.setMaxCpu(max);
                    promotionData.setArgRTBefore(beforeRT);
                    promotionData.setArgRT(currentRT);
                    promotionData.setAppException(exData);
                    promotionData.setStartTime(startTime);
                    promotionLog(appId, address, reasonable, GovernanceType.RELEASE_TRAFFIC, promotionData);
                    // 两倍 && > 1s
                    if (currentRT > 1 && currentRT > (beforeRT * 2)) {
                        sampleNotify(appId, InstanceUtils.assembleInstanceId(appId, address), promotionData);
                    }
                }
            } catch (Exception e) {
                log.error("releaseTrafficReasonableRunner fail:", e);
            }
        };
    }


    private Runnable warmUpReasonableRunner(String appId, String address, long delayTime, long startTime, long warmUpTime) {
        return () -> {
            long endTime = startTime + warmUpTime;
            RespMsg<SeriesBo> respMsg = rpcMetricService.podCpuUseMetric(appId, address, startTime, endTime);
            if (respMsg.isSuccess()) {
                List<PointBo> series = respMsg.getData().getSeries();
                if (CollUtil.isEmpty(series)) {
                    return;
                }
                double cpu = series.get(series.size() - 1).getValue();
                boolean reasonable = cpu < 70;
                //不合理再进行插入
                if (reasonable) {
                    return;
                }
                WarmUpPromotionData promotionData = new WarmUpPromotionData();
                promotionData.setFinalCpu(cpu);
                promotionData.setStartTime(startTime);
                promotionData.setWarmUpTime(warmUpTime);
                promotionData.setDelayTime(delayTime);
                promotionLog(appId, address, reasonable, GovernanceType.WARMUP, promotionData);
            }
        };
    }

    private void promotionLog(String appId, String address, boolean reasonable, GovernanceType type, Object data) {
        GovernancePromotionLog promotionLog = new GovernancePromotionLog();
        promotionLog.setReasonable(reasonable);
        promotionLog.setAppId(appId);
        promotionLog.setInstanceId(InstanceUtils.assembleInstanceId(appId, address));
        promotionLog.setType(type);
        promotionLog.setData(JacksonUtils.serialize(data, new TypeReference<>() {
        }));
        governancePromotionLogMapper.insert(promotionLog);
    }


    public void sampleNotify(String appId,String instanceId,ReleaseTrafficPromotionData data) {
        AppWarmUpAbnormalData build = AppWarmUpAbnormalData.builder()
                .appId(appId)
                .exceptions(data.getAppException().entrySet().stream().map(item -> item.getKey() + item.getValue()).collect(Collectors.toList()))
                .instanceId(instanceId)
                .currentCpu(data.getMaxCpu())
                .upBeforeArgRt(data.getArgRTBefore() * 1000)
                .upMaxRt(data.getArgRT() * 1000)
                .triggerTime(data.getStartTime())
                .build();
        simpleMessageService.releaseTrafficReasonableNotify(build);
    }
}
