import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {AppBaseService} from "../../app-base.service";
import {LocalStorageService} from "ngx-localstorage";
import {SpinningService} from "../../service/spinning.service";
import {UserService} from "../../user.service";
import {PlatformMessageComponent} from "../platform-message/platform-message.component";

@Component({
  selector: 'app-red-point',
  templateUrl: './red-point.component.html',
  styleUrls: ['./red-point.component.less']
})
export class RedPointComponent extends AppBaseService implements OnInit {

  constructor(
    private userService: UserService,
    private spinningService: SpinningService,
    public override localStorageService: LocalStorageService) {
    super(localStorageService)
  }


  unReadSize: number = 0;

  messagePageModel: any = {};
  @Output() clickRead = new EventEmitter();
  @ViewChild("appPlatformMessage") appPlatformMessage!: PlatformMessageComponent

  oneClickRead() {
    this.spinningService.open();
    this.userService.platformMessageOneClickRead().subscribe(resp => {
      if (resp.success) {
        this.userMessagePage(0)
        this.getUnReadSize()
        this.spinningService.close();
        this.clickRead.emit()
      }
    })
  }


  ngOnInit(): void {
    this.userMessagePage(0)
    this.userService.platformMessagePage(5)
  }

  userMessagePage(index: number) {
    this.spinningService.open();
    this.userService.platformMessagePage(index).subscribe(resp => {
      if (resp.success) {
        this.messagePageModel = resp.data;
      }
      this.spinningService.close();
    })
  }

  getUnReadSize() {
    this.spinningService.open();
    this.userService.platformMessageUnRead().subscribe(resp => {
      if (resp.success) {
        this.unReadSize = resp.data;
      }
      this.spinningService.close();
    })
  }

  clickOpenDetail(data: any) {
    this.appPlatformMessage.open(data)
    data.state = true;
    this.getUnReadSize()
    this.clickRead.emit()
  }
}
