import { Component } from '@angular/core';
import {ConfigService} from "../../pages/config/config.service";
import {AppBaseService} from "../../app-base.service";
import {LocalStorageService} from "ngx-localstorage";
import {concat, isEmpty} from "lodash";
import {NzMessageService} from "ng-zorro-antd/message";

@Component({
  selector: 'component-namespace-release',
  templateUrl: './namespace-release.component.html',
  styleUrls: ['./namespace-release.component.less']
})
export class NamespaceReleaseComponent extends AppBaseService{


  constructor(private configService : ConfigService,
              private messageService : NzMessageService,
              localStorageService: LocalStorageService) {
    super(localStorageService)
  }


  appId !: string;
  cluster !: string
  namespace !: string;
  num : number = 0;
  visible : boolean = false;
  detailVisible : boolean = false;
  releaseNamespaceModel : any = [];
  releaseNamespaceDetailModel: any = []



  open(appId : string,cluster : string,namespace : string){
    this.appId = appId;
    this.cluster = cluster;
    this.namespace = namespace;
    this.namespaceReleaseLog();
    this.visible = true;
  }


  private namespaceReleaseLog(){
    this.configService.namespaceReleaseLog(this.appId,this.cluster,this.namespace,this.num,this.getPageSize()).subscribe(resp =>{
      if (resp.success){
        this.releaseNamespaceModel = concat(this.releaseNamespaceModel,resp.data);
        if (isEmpty(resp.data)){
          this.messageService.info("无更多数据");
        }
      }
    })
  }

  close() : void{
    this.num = 0;
    this.visible = false;
    this.releaseNamespaceModel = [];
    this.releaseNamespaceDetailModel = [];
  }

  next() {
    this.num ++;
    this.namespaceReleaseLog();
  }


  closeDetail() : void{
    this.detailVisible = false;
  }

  releaseDetail(releaseId: string,previousReleaseId :string ) {
    this.configService.namespaceReleaseDetail(releaseId,previousReleaseId).subscribe(resp =>{
      if (resp.success){
        this.releaseNamespaceDetailModel = resp.data?.changes;
        this.detailVisible = true;
      }
    })
  }
}

