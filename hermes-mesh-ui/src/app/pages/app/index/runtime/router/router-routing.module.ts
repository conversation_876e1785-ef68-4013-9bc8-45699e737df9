import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {RouterComponent} from "./router.component";
import {ConsumerComponent} from "./consumer/consumer.component";
import {ProviderComponent} from "./provider/provider.component";

const routes: Routes = [{
  path:'',component:RouterComponent,children:[
    {path:'consumer',component:ConsumerComponent},
    {path:'provider',component:ProviderComponent},
    {path:'',redirectTo:'consumer',pathMatch:'full'},
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RouterRoutingModule { }
