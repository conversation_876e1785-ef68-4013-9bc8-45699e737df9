package cn.huolala.arch.hermes.mesh.component.lark.model.message;

public interface ContentConstant {

    class Base {
        public static final String BOLD_FORMAT = "**%s** ";
        public static final String AT_VALUE = "<at email=%<EMAIL>></at>";
        public static final String APPID_KEY = "💻 APPID";
        public static final String ENV_KEY = "📌 环境";
        public static final String REGION_KEY = "⛳️ 区域";
        public static final String DEPLOY_KEY = "🛣 部署";
        public static final String TRIGGER_TIME_KEY = "⏰ 触发时间";
        public static final String LEVEL_KEY = "🔉 级别";
        public static final String DETAIL_CONTENT_KEY = "🔢 具体内容";
        public static final String COMPONENT_KEY = "💻 组件";
    }
}
