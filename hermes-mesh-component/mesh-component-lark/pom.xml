<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.huolala.arch.hermes</groupId>
		<artifactId>hermes-mesh-component</artifactId>
		<version>${revision}</version>
	</parent>
	<artifactId>mesh-component-lark</artifactId>


	<dependencies>
		<dependency>
			<groupId>com.github.lianjiatech</groupId>
			<artifactId>retrofit-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>

		<dependency>
			<groupId>com.larksuite.oapi</groupId>
			<artifactId>oapi-sdk</artifactId>
			<version>${lark.open.api.verion}</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>

		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>

		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>${jsoup.version}</version>
		</dependency>
	</dependencies>

</project>
