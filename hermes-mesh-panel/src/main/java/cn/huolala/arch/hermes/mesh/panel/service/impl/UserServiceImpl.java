package cn.huolala.arch.hermes.mesh.panel.service.impl;

import cn.huolala.arch.hermes.mesh.component.cmdb.model.UserApp;
import cn.huolala.arch.hermes.mesh.component.cmdb.service.CmdbFacadeService;
import cn.huolala.arch.hermes.mesh.panel.entity.*;
import cn.huolala.arch.hermes.mesh.panel.enums.PlatformNoticeType;
import cn.huolala.arch.hermes.mesh.panel.mapper.*;
import cn.huolala.arch.hermes.mesh.panel.service.UserService;
import cn.huolala.arch.hermes.mesh.panel.vos.user.UserPlatFormMessageVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Slf4j
@Service
public class UserServiceImpl implements UserService, InitializingBean {

    private static final ScheduledExecutorService LOAD_MESSAGE_EXECUTOR = Executors.newSingleThreadScheduledExecutor();

    @Resource
    private UserLogMapper userLogMapper;

    @Resource
    private CmdbFacadeService cmdbFacadeService;

    @Resource
    private UserLarkAccessTokenMapper userLarkAccessTokenMapper;

    @Resource
    private PlatformUserMessageMapper platformUserMessageMapper;

    @Resource
    private PlatformMessageNoticeMapper platformMessageNoticeMapper;

    @Resource
    private UserDefinedSettingMapper userDefinedSettingMapper;

    @Override
    public void userLog(UserLog userLog) {
        userLogMapper.insert(userLog);
    }

    @Override
    public String larkAccessToken(String userName) {
        QueryWrapper<UserLarkAccessToken> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", userName);
        UserLarkAccessToken larkAccessToken = userLarkAccessTokenMapper.selectOne(queryWrapper);
        if (Objects.nonNull(larkAccessToken)) {
            return larkAccessToken.getAccessToken();
        }
        return null;
    }

    @Override
    public String adminLarkAccessTokenFirst() {
        return Optional.ofNullable(userLarkAccessTokenMapper.selectList(null)).orElse(new ArrayList<>())
                .stream().findFirst().map(UserLarkAccessToken::getAccessToken).orElse(null);
    }

    @Override
    public void larkAccessTokenCache(UserLarkAccessToken accessToken) {
        userLarkAccessTokenMapper.insert(accessToken);
    }

    @Override
    public List<String> platFormUsers() {
        return userLogMapper.users();
    }

    @Override
    public Long platFormMessageUnRead(String userName) {
        return platformUserMessageMapper.selectUnRead(userName);
    }

    @Override
    public void platFormMessageNotice(PlatformMessageNotice platformMessageNotice) {
        platformMessageNoticeMapper.insert(platformMessageNotice);
    }

    @Override
    public Page<UserPlatFormMessageVo> platFormMessageList(String userName, int num, int size) {
        Page<PlatformUserMessage> messagePage = platformUserMessageMapper.selectByUseId(userName, Page.of(num, size));
        List<UserPlatFormMessageVo> messageVos = messagePage.getRecords().stream().map(message -> {
            UserPlatFormMessageVo messageVo = new UserPlatFormMessageVo();
            PlatformMessageNotice notice = platformMessageNoticeMapper.selectById(message.getPlatformNoticeId());
            if (Objects.isNull(notice)) {
                return null;
            }
            messageVo.setPlatformNoticeId(message.getPlatformNoticeId());
            messageVo.setId(message.getId());
            messageVo.setState(message.isState());
            messageVo.setColor(notice.getColor());
            messageVo.setDesc(notice.getDesc());
            messageVo.setTitle(notice.getTitle());
            messageVo.setCreatedAt(notice.getPublishTime().getTime());
            return messageVo;
        }).filter(Objects::nonNull).toList();
        Page<UserPlatFormMessageVo> pageResult = Page.of(messagePage.getCurrent(), messagePage.getSize(), messagePage.getTotal(), messagePage.searchCount());
        return pageResult.setRecords(messageVos);
    }

    @Override
    public PlatformMessageNotice platFormMessageDetail(UserPlatFormMessageVo messageVo) {
        platformUserMessageMapper.updateStateById(messageVo.getId(), true);
        return platformMessageNoticeMapper.selectById(messageVo.getPlatformNoticeId());
    }

    @Override
    public void oneClickRead(String userName) {
        platformUserMessageMapper.updateStateToTrueByUserId(userName);
    }

    @Override
    public List<String> dashboardAppIdOrDefault(String userName) {
        UserDefinedSetting userDefinedSetting = userDefinedSettingMapper.selectByUserId(userName);
        return Optional.ofNullable(userDefinedSetting).map(UserDefinedSetting::getData).map(UserDefinedSetting.Data::getDashBroadAppIds)
                .filter(CollUtil::isNotEmpty).orElseGet(
                        () -> Optional.ofNullable(cmdbFacadeService.userApps(userName).getList())
                                .orElse(ListUtil.empty()).stream().map(UserApp.UserAppInfo::getAppId).collect(Collectors.toList()));
    }

    @Override
    public void resetDashboardAppId(String userName) {
        UserDefinedSetting userDefinedSetting = userDefinedSettingMapper.selectByUserId(userName);
        Optional.ofNullable(userDefinedSetting).ifPresent(item -> {
            item.getData().setDashBroadAppIds(new ArrayList<>());
            userDefinedSettingMapper.updateById(item);
        });
    }

    @Override
    public void updateDashboardAppIds(String userName, List<String> appIds) {
        UserDefinedSetting userDefinedSetting = userDefinedSettingMapper.selectByUserId(userName);
        if (Objects.isNull(userDefinedSetting)) {
            userDefinedSetting = new UserDefinedSetting();
            userDefinedSetting.setUserId(userName);
            UserDefinedSetting.Data data = new UserDefinedSetting.Data();
            data.setDashBroadAppIds(appIds);
            userDefinedSetting.setData(data);
            userDefinedSettingMapper.insert(userDefinedSetting);
        } else {
            userDefinedSetting.getData().setDashBroadAppIds(appIds);
            userDefinedSettingMapper.updateById(userDefinedSetting);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            cmdbFacadeService.forceRefresh();
            // 定时5min拉取，进行消息的发布
            LOAD_MESSAGE_EXECUTOR.scheduleAtFixedRate(this::loadPlatformMessageToUser, 0, 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("load platform message fail", e);
        }
    }


    private void loadPlatformMessageToUser() {
        log.info("begin to load platform message...");
        List<PlatformMessageNotice> platformMessageNotices = platformMessageNoticeMapper.selectUnPublish();
        AtomicLong noticePublish = new AtomicLong(0);
        for (PlatformMessageNotice platformMessageNotice : platformMessageNotices) {
            if (platformMessageNotice.getPublishTime().getTime() > System.currentTimeMillis()) {
                continue;
            }
            try {
                log.info("platform message: {} is time to publish", platformMessageNotice.getTitle());
                // 达到发布时间，进行发布
                noticePublish.incrementAndGet();
                String noticeType = platformMessageNotice.getNoticeType();
                if (noticeType.equals(PlatformNoticeType.ALL.name())) {
                    processAllNoticeType(platformMessageNotice);
                } else if (noticeType.equals(PlatformNoticeType.APP.name())) {
                    processAppNoticeType(platformMessageNotice);
                } else if (noticeType.equals(PlatformNoticeType.USER.name())) {
                    processUserNoticeType(platformMessageNotice);
                }
                // 发布后需要将状态设置为已拉取
                platformMessageNotice.setState(true);
                platformMessageNoticeMapper.updateById(platformMessageNotice);
                log.info("platform message: {} ", platformMessageNotice.getTitle());
            } catch (Exception e) {
                log.info("platform message: {} publish fail", platformMessageNotice.getTitle(), e);
            }
        }
        log.info("ending load platform message, count {} message", noticePublish.get());
    }

    /**
     * 处理公告消息
     */
    private void processAllNoticeType(PlatformMessageNotice platformMessageNotice) {
        List<String> userIds = userLogMapper.users();
        userIds.forEach(user -> insertUserMessage(user, platformMessageNotice.getId()));
    }


    /**
     * 处理指定App消息
     */
    private void processAppNoticeType(PlatformMessageNotice platformMessageNotice) {
        platformMessageNotice.getNoticeTo().forEach(appId -> {
            Set<String> userId = cmdbFacadeService.appUserId(appId, true, false, true, false);
            userId.forEach(user -> insertUserMessage(user, platformMessageNotice.getId()));
        });
    }

    /**
     * 处理指定user消息
     */
    private void processUserNoticeType(PlatformMessageNotice platformMessageNotice) {
        platformMessageNotice.getNoticeTo().forEach(user -> insertUserMessage(user, platformMessageNotice.getId()));
    }

    private void insertUserMessage(String userId, long noticeId) {
        // 消息是否存在，不存再进行写入
        if (!platformUserMessageMapper.exist(userId, noticeId)) {
            try {
                PlatformUserMessage platformUserMessage = new PlatformUserMessage();
                platformUserMessage.setUserId(userId);
                platformUserMessage.setPlatformNoticeId(noticeId);
                platformUserMessage.setState(false);
                platformUserMessageMapper.insert(platformUserMessage);
            } catch (Exception e) {
                log.info("add user message fail: {}", e.getMessage());
            }
        }
    }
}
