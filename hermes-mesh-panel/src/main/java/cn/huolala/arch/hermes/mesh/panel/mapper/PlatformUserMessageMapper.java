package cn.huolala.arch.hermes.mesh.panel.mapper;

import cn.huolala.arch.hermes.mesh.panel.entity.PlatformUserMessage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformUserMessageMapper extends BaseMapper<PlatformUserMessage> {

    default Long selectUnRead(String userId) {
        QueryWrapper<PlatformUserMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("state", 0);
        return selectCount(queryWrapper);
    }

    default Page<PlatformUserMessage> selectByUseId(String userId, Page<PlatformUserMessage> page) {
        QueryWrapper<PlatformUserMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("id");
        return selectPage(page, queryWrapper);
    }

    default boolean exist(String userId, long platformNoticeId) {
        QueryWrapper<PlatformUserMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("platform_notice_id", platformNoticeId);
        return exists(queryWrapper);
    }

    default List<PlatformUserMessage> selectByPlatformNoticeId(long platformNoticeId) {
        QueryWrapper<PlatformUserMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("platform_notice_id", platformNoticeId);
        return selectList(queryWrapper);
    }

    default void deleteByPlatformNoticeId(long platformNoticeId) {
        QueryWrapper<PlatformUserMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("platform_notice_id", platformNoticeId);
        delete(queryWrapper);
    }

    void updateStateById(@Param("id") Long id, @Param("state") boolean state);

    void updateStateToTrueByUserId(@Param("userId") String userId);

}
