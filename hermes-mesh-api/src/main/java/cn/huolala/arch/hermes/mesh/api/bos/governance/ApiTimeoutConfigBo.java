package cn.huolala.arch.hermes.mesh.api.bos.governance;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApiTimeoutConfigBo extends ApiConfigBo{

    /**
     * commandKey
     */
    private String name;

    /**
     * 读超时时间
     */
    private int readTimeout;

    /**
     * 连接超时时间
     */
    private int connectionTimeout;

    /**
     * true 开始 false 关闭
     */
    private boolean enabled;

}
