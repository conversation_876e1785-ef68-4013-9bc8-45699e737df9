package cn.huolala.arch.hermes.mesh.api.bos.open;

import cn.huolala.arch.hermes.mesh.api.bos.Bo;
import cn.huolala.arch.hermes.mesh.common.enums.ComponentStatus;
import cn.huolala.arch.hermes.mesh.common.enums.ComponentType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ComponentStatusBo extends Bo {

    /**
     * 所属APPID的检查
     */
    private String appId;

    /**
     * 组件类型
     */
    private ComponentType type;

    /**
     * 组件状态
     */
    private ComponentStatus status;

    /**
     * 具体内容
     */
    private Map<String, Object> detail = new HashMap<>();

}
