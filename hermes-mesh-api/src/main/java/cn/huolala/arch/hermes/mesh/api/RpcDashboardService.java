package cn.huolala.arch.hermes.mesh.api;

import cn.huolala.arch.hermes.mesh.api.bos.KVBo;
import cn.huolala.arch.hermes.mesh.api.bos.SeriesBo;
import cn.huolala.arch.hermes.mesh.api.bos.app.AppEventBo;
import cn.huolala.arch.hermes.mesh.api.bos.dashboard.DashboardCallMetricBo;
import cn.huolala.arch.hermes.mesh.api.bos.dashboard.DashboardGovernLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.event.ApiCircuitEventLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.governance.AppZoneRouterLogBo;
import cn.huolala.arch.hermes.mesh.api.bos.operation.AppAccessBo;
import cn.huolala.arch.hermes.mesh.api.bos.operation.DataCommandKeyNumBo;
import cn.huolala.arch.hermes.mesh.api.bos.registry.ServiceBo;
import cn.huolala.arch.hermes.mesh.common.enums.GovernanceLevel;
import cn.huolala.arch.hermes.mesh.common.message.RespMsg;
import cn.lalaframework.soa.annotation.SOAService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

@SOAService(value = "/rpcDashboardService", appId = "ci-hermes-sentinel-svc")
public interface RpcDashboardService {

    /**
     * App版本数据
     */
    RespMsg<List<AppAccessBo>> appVersion(List<String> appIds);


    /**
     * App接入数据
     */
    RespMsg<List<AppAccessBo>> appAccess(List<String> appIds);


    /**
     * App规范排行榜
     */
    RespMsg<List<KVBo<String, Double>>> appSpecificationRanking(List<String> appIds);

    /**
     * App规范趋势
     */
    RespMsg<List<SeriesBo>> appSpecificationMetric(List<String> appIds, long startTime, long endTime);

    /**
     * App接口类型数量列表详情
     */
    RespMsg<List<DataCommandKeyNumBo>> appCommandKeyTypeList(List<String> appIds);


    /**
     * 类型指标
     */
    RespMsg<DataCommandKeyNumBo> appCommandKeyView(List<String> appIds);


    /**
     * App接口类型指标
     */
    RespMsg<List<DataCommandKeyNumBo>> appCommandKeyMetric(List<String> appIds, long startTime, long endTime);


    /**
     * App的熔断走fallback的qps
     */
    RespMsg<List<SeriesBo>> appCircuitBreakerFallbackMetric(List<String> appIds, long startTime, long endTime);


    /**
     * App的降级走fallback的qps
     */
    RespMsg<List<SeriesBo>> appDegradationFallbackMetric(List<String> appIds, long startTime, long endTime);


    /**
     * App客户端限流走fallback的qps
     */
    RespMsg<List<SeriesBo>> appConsumerRateLimitMetric(List<String> appIds, long startTime, long endTime);


    /**
     * App服务端限流走fallback的qps
     */
    RespMsg<List<SeriesBo>> appProviderRateLimitMetric(List<String> appIds, GovernanceLevel level, long startTime, long endTime);


    /**
     * TODO App的鉴权失败：
     */
    RespMsg<List<SeriesBo>> appAuthFailMetric(List<String> appIds, long startTime, long endTime);


    /**
     * 熔断事件
     */
    RespMsg<Page<ApiCircuitEventLogBo>> appCircuitEvent(List<String> appIds, long startTime, long endTime, int num, int size);


    /**
     * 操作日志
     */
    RespMsg<Page<DashboardGovernLogBo>> appGovernLog(List<String> appIds, int num, int size);


    /**
     * 节点数据
     */
    RespMsg<List<ServiceBo>> appLifeServiceData(List<String> appIds);


    /**
     * Life Event
     */
    RespMsg<Page<AppEventBo>> appLifeEvent(List<String> appIds, long startTime, long endTime, int num, int size);


    /**
     * APP RPC调用指标
     */
    RespMsg<DashboardCallMetricBo> appCallMetric(List<String> appIds, long startTime, long endTime);


    /**
     * AZ Router Log
     */
    RespMsg<Page<AppZoneRouterLogBo>> appCallZoneLog(List<String> appIds, int num, int size);

}
