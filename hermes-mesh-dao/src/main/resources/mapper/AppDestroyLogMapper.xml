<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.huolala.arch.hermes.mesh.dao.mapper.AppDestroyLogMapper">

    <select id="selectPage" resultType="cn.huolala.arch.hermes.mesh.dao.entity.AppDestroyLog">
        select * from app_destroy_log where app_id =#{appId}
        <if test="host != null and host != ''">
            and host =#{host}
        </if>
        order by id desc
    </select>

    <select id="selectCollapsePage" resultType="cn.huolala.arch.hermes.mesh.dao.entity.AppEventCollapse">
        select count(host)     as count,
               min(created_at) as createdAt,
               max(updated_at) as updatedAt,
               host            as
                                  instanceId
        from app_destroy_log
        where app_id = #{appId}
        group by host
        order by updatedAt desc
    </select>

</mapper>
