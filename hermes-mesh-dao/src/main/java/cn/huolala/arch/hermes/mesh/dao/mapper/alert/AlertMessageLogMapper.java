package cn.huolala.arch.hermes.mesh.dao.mapper.alert;


import cn.huolala.arch.hermes.mesh.common.enums.AlertEventType;
import cn.huolala.arch.hermes.mesh.dao.entity.alert.AlertMessageLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface AlertMessageLogMapper extends BaseMapper<AlertMessageLog> {

    Page<AlertMessageLog> selectPage(Page<?> page, @Param("appId") String appId, @Param("alertEventType") AlertEventType alertEventType);

    Page<AlertMessageLog> selectAppIdPage(Page<?> page, @Param("appId") String appId);


}
