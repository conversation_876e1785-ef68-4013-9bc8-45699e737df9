package cn.huolala.arch.hermes.mesh.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @see cn.huolala.arch.hermes.mesh.dao.handler.ApiAuthAppConfigHandler
 */
@Data
@TableName(value = "api_auth_app_config",excludeProperty = {"operationType"})
@EqualsAndHashCode(callSuper = true)
public class ApiAuthAppConfig extends BaseEntity {

    /**
     * 当前服务appId
     */
    private String appId;

    /**
     * 服务名
     */
    private String name;

    /**
     * 授权appId
     */
    private String authAppId;

    /**
     * cluster
     */
    private String cluster;

    /**
     * 授权生效时间
     */
    private long effectiveAt;

    /**
     * 授权截止时间
     */
    private long expiredAt;

    /**
     * 0: 删除 1：新增 3：更新
     * 兼容 旧版
     */
    private int operationType;
}
