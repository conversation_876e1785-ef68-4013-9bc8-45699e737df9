package cn.huolala.arch.hermes.mesh.dao.entity.consul;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @see cn.huolala.arch.hermes.mesh.dao.handler.CheckDataSetHandler
 */
@Data
@EqualsAndHashCode
public class CheckData {

    /**
     * ConsulAgent名字
     */
    private String nodeName;


    /**
     * ConsulAgent节点IP
     */
    private String nodeAddress;


    /**
     * 服务instanceId：
     * bdi-datacloud-api-10-129-32-21-management
     */
    private String serviceId;


    /**
     * 所属的serviceName：
     * bdi-datacloud-api-management
     */
    private String serviceName;


    /**
     * tags
     */
    private List<String> serviceTags;


    /**
     * status
     */
    private String status;


    /**
     * check类型
     */
    private String type;

}
