package cn.huolala.arch.hermes.mesh.dao.entity;

import cn.huolala.arch.hermes.mesh.common.enums.ComponentStatus;
import cn.huolala.arch.hermes.mesh.common.enums.ComponentType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "component_check_log", autoResultMap = true)
public class ComponentCheckLog extends BaseEntity {

    /**
     * 检查者AppId
     */
    private String checkerAppId;

    /**
     * 组件类型
     */
    private ComponentType type;


    /**
     * 自检appId
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> checkAppId;


    /**
     * 自检结果
     */
    private ComponentStatus status;


    /**
     * 自检结果详情
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ComponentStatusData> detail;
}
