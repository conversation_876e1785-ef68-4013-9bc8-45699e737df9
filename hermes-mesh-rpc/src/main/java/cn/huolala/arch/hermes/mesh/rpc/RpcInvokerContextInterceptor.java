package cn.huolala.arch.hermes.mesh.rpc;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.ClientInterceptor;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.InterceptorChain;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.lalaframework.tools.util.StrUtil;

@Activate
public class RpcInvokerContextInterceptor implements ClientInterceptor {

    @Override
    public Result invoke(InterceptorChain chain, Invocation invocation) throws RpcException {
        String host = RpcInvokerContext.getContext().getHost();
        int port = RpcInvokerContext.getContext().getPort();
        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) invocation.get(JsonRpcInvocation.class.getName());
        if (StrUtil.isNotEmpty(host)) {
            jsonRpcInvocation.setHost(host);
        }
        if (port > 0) {
            jsonRpcInvocation.setPort(port);
        }
        jsonRpcInvocation.setHttps(RpcInvokerContext.getContext().isHttps());
        try {
            return chain.invoke(invocation);
        } catch (Exception e) {
            return RpcResult.newRpcResult(e, invocation);
        }
    }

}
